# 🇨🇮 Softéa - Surveillance Intelligente de l'Orpaillage

![Softéa Logo](https://img.shields.io/badge/Softéa-Surveillance%20Orpaillage-orange?style=for-the-badge&logo=shield&logoColor=white)

## 📋 Description

**Softéa** est une plateforme de surveillance intelligente dédiée à la détection et au monitoring des activités d'orpaillage illégal en Côte d'Ivoire. Cette application web moderne offre une interface intuitive pour les autorités gouvernementales afin de surveiller, analyser et gérer les sites d'orpaillage sur le territoire national.

## ✨ Fonctionnalités Principales

### 🗺️ **Cartographie Interactive**
- Carte satellite de la Côte d'Ivoire avec Leaflet.js
- Visualisation en temps réel des sites d'orpaillage
- Contrôles de couches multiples (satellite, standard, terrain)
- Zoom et navigation fluides

### 📊 **Tableau de Bord Analytique**
- Statistiques en temps réel des détections
- Graphiques d'évolution temporelle
- Répartition géographique des activités
- Alertes par niveau de criticité

### 🚨 **Système d'Alertes**
- Notifications en temps réel
- Classification par niveau de priorité
- Historique complet des alertes
- Gestion des interventions

### 📈 **Rapports et Analyses**
- Génération de rapports automatisés
- Analyses de tendances
- Exportation de données
- Historique des activités

### 👥 **Gestion Multi-Niveaux**
- **Niveau National** : Vue d'ensemble complète
- **Niveau Régional** : Surveillance régionale
- **Niveau Local** : Monitoring local

## 🛠️ Technologies Utilisées

### Frontend
- **React 18** - Framework JavaScript moderne
- **Vite** - Build tool ultra-rapide
- **Tailwind CSS** - Framework CSS utilitaire
- **React Router** - Navigation SPA
- **Leaflet.js** - Cartographie interactive
- **Heroicons** - Icônes modernes

### Styling & Design
- **Design System Ivoirien** - Couleurs authentiques CI
- **Responsive Design** - Compatible mobile/desktop
- **Animations CSS** - Transitions fluides
- **Gradients Personnalisés** - Thème orange/vert CI

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- npm ou yarn
- Git

### Installation
```bash
# Cloner le repository
git clone https://gitlab.com/ada2-20251654408/********************orpaillage.git
cd ********************orpaillage

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

### Scripts Disponibles
```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Aperçu du build
npm run lint         # Vérification du code
```

## 🔐 Authentification

### Comptes de Test
```
Email: <EMAIL>
Mot de passe: password123
Code 2FA: 123456
Niveau: NATIONAL

Email: <EMAIL>
Mot de passe: password123
Code 2FA: 123456
Niveau: REGIONAL
```

## 🎨 Design System

### Palette de Couleurs Ivoiriennes
- **Orange CI** : `#FF8C00` - Couleur principale
- **Vert CI** : `#22C55E` - Couleur secondaire
- **Blanc** : `#FFFFFF` - Arrière-plans
- **Gris** : `#E0E0E0` - Éléments neutres

### Composants Principaux
- **SidebarFixed** - Navigation latérale responsive
- **Navbar** - Barre de navigation supérieure
- **Avatar** - Composant avatar avec photo par défaut
- **Dashboard** - Tableau de bord principal
- **MapViewSimple** - Carte interactive

## 📱 Responsive Design

L'application est entièrement responsive et s'adapte à :
- **Desktop** : 1920px+
- **Laptop** : 1024px - 1919px
- **Tablet** : 768px - 1023px
- **Mobile** : 320px - 767px

## 🔒 Sécurité

- Authentification à deux facteurs (2FA)
- Gestion des sessions sécurisées
- Contrôle d'accès basé sur les rôles
- Validation côté client et serveur

## 🌍 Déploiement

### Variables d'Environnement
```env
VITE_API_URL=https://api.softea.gouv.ci
VITE_MAP_API_KEY=your_map_api_key
VITE_ENVIRONMENT=production
```

### Build de Production
```bash
npm run build
# Les fichiers sont générés dans le dossier 'dist/'
```

## 👥 Équipe de Développement

- **Développeur Principal** : [Votre Nom]
- **Designer UI/UX** : [Nom Designer]
- **Chef de Projet** : [Nom Chef Projet]

## 📄 Licence

Ce projet est développé pour le Gouvernement de Côte d'Ivoire.
Tous droits réservés © 2024 République de Côte d'Ivoire.

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -m 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou support technique :
- **Email** : <EMAIL>
- **Documentation** : [Lien vers la doc]
- **Issues** : Utiliser le système d'issues GitLab

---

**Softéa** - *Surveillance Intelligente pour une Côte d'Ivoire Durable* 🇨🇮
