from django.contrib.auth.base_user import BaseUserManager, AbstractBaseUser
from django.db import models
from base.models.helpers.date_time_model import DateTimeModel


class CustomUserManager(BaseUserManager):

    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('Email obligatoire')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)


class UserModel(AbstractBaseUser, DateTimeModel):
    """Custom User model with email authentication and authority system"""

    email = models.EmailField(unique=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)

    # Personal information
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    phone = models.CharField(max_length=150, blank=True)
    address = models.CharField(max_length=150, blank=True)
    job_title = models.CharField(max_length=150, blank=True, verbose_name="Fonction")

    # Regional access (for mining detection system)
    authorized_region = models.CharField(max_length=100, blank=True)
    access_level = models.CharField(max_length=20, default='REGIONAL')
    institution = models.CharField(max_length=100, blank=True)

    # System metadata
    last_login_date = models.DateTimeField(auto_now=True)
    secure_connection = models.BooleanField(default=True)

    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        db_table = 'user'
        verbose_name = 'Utilisateur'
        verbose_name_plural = 'Utilisateurs'

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return full name"""
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return first name"""
        return self.first_name

    def get_primary_authority(self):
        """Get the primary authority/role for this user"""
        try:
            primary_auth = self.user_authorities.filter(is_primary=True, status=True).first()
            return primary_auth.authority.name if primary_auth else "Aucune autorité"
        except:
            return "Aucune autorité"

    def get_all_authorities(self):
        """Get all active authorities for this user"""
        try:
            return [ua.authority for ua in self.user_authorities.filter(status=True)]
        except:
            return []

    def has_authority(self, authority_code):
        """Check if user has a specific authority"""
        try:
            return self.user_authorities.filter(
                authority__code=authority_code,
                status=True
            ).exists()
        except:
            return False

    def has_perm(self, perm, obj=None):
        """For Django admin compatibility"""
        return self.is_active and self.is_superuser

    def has_module_perms(self, app_label):
        """For Django admin compatibility"""
        return self.is_active and self.is_superuser

    # DRF Permission helpers
    def can_create_users(self):
        """Check if user can create other users (for DRF permissions)"""
        return (self.is_superuser or
                self.has_authority('SUPERADMIN') or
                self.has_authority('ADMIN'))

    def can_manage_region(self, region_code):
        """Check if user can manage a specific region"""
        return (self.authorized_region == 'ALL' or
                self.authorized_region == region_code or
                self.has_authority('SUPERADMIN'))

    def can_validate_detections(self):
        """Check if user can validate AI detections"""
        return (self.has_authority('REGIONAL_DIRECTOR') or
                self.has_authority('REGIONAL_MANAGER') or
                self.has_authority('TECHNICAL_AGENT') or
                self.is_superuser)

    def can_generate_reports(self):
        """Check if user can generate reports"""
        return (self.has_authority('REGIONAL_DIRECTOR') or
                self.has_authority('REGIONAL_MANAGER') or
                self.has_authority('ANALYST_AGENT') or
                self.is_superuser)