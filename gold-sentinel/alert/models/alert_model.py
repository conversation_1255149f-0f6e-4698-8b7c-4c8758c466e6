from django.db import models
from base.models.helpers.named_date_time_model import NamedDateTimeModel


class AlertModel(NamedDateTimeModel):
    """Mining alerts with criticality levels"""

    CRITICALITY_LEVELS = [
        ('CRITICAL', 'Critical'),  # Red - High priority
        ('MEDIUM', 'Medium'),  # Orange - Medium priority
        ('LOW', 'Low'),  # Green - Low priority
    ]

    ALERT_TYPES = [
        ('CLANDESTINE_SITE', 'Clandestine site detected'),
        ('SITE_EXPANSION', 'Possible expansion of existing site'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious activity detected by AI'),
        ('NEW_SITE', 'New site detected near river'),
        ('PROTECTED_ZONE_BREACH', 'Activity in protected zone'),
    ]

    ALERT_STATUS = [
        ('ACTIVE', 'Active'),
        ('ACKNOWLEDGED', 'Acknowledged'),
        ('RESOLVED', 'Resolved'),
        ('FALSE_ALARM', 'False Alarm'),
    ]

    # Relations
    detection = models.ForeignKey('detections.Detection', on_delete=models.CASCADE)
    region = models.ForeignKey('regions.Region', on_delete=models.CASCADE)

    # Alert details (name inherited from NamedDateTimeModel serves as title)
    criticality_level = models.CharField(max_length=20, choices=CRITICALITY_LEVELS)
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPES)
    message = models.TextField()

    # Processing
    alert_status = models.CharField(max_length=20, choices=ALERT_STATUS, default='ACTIVE',
                                    help_text="Use alert_status to avoid conflict with inherited status field")
    is_read = models.BooleanField(default=False)

    # Assignment
    assigned_to = models.ForeignKey('users.User', on_delete=models.SET_NULL,
                                    null=True, blank=True, related_name='assigned_alerts')
    acknowledged_by = models.ForeignKey('users.User', on_delete=models.SET_NULL,
                                        null=True, blank=True, related_name='acknowledged_alerts')

    # Timestamps (created_at/updated_at inherited)
    acknowledged_date = models.DateTimeField(null=True, blank=True)
    resolved_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'mining_alerts'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['criticality_level', 'alert_status']),
            models.Index(fields=['region', 'created_at']),
            models.Index(fields=['alert_status', 'is_read']),
        ]

    @property
    def time_since_created(self):
        """Calculate time since alert creation (for frontend display)"""
        from django.utils import timezone
        import datetime

        now = timezone.now()
        diff = now - self.created_at

        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        else:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"