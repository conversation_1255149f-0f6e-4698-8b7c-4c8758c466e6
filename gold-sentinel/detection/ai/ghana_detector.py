"""
Détecteur d'orpaillage basé sur le dataset Ghana d'Earthrise Media
Adapté pour la Côte d'Ivoire
"""
import os
import numpy as np
import cv2
import logging
from pathlib import Path
from django.conf import settings
from django.core.files.storage import default_storage
from detection.models.report_model import DetectionModel
from PIL import Image

logger = logging.getLogger('detection')

try:
    os.environ['CUDA_VISIBLE_DEVICES'] = ''  # Désactiver GPU
    import tensorflow as tf
    tf.config.set_visible_devices([], 'GPU')  # Forcer CPU
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow non disponible, mode simulation activé")

class GhanaBasedDetector:
    def __init__(self):
        self.model_path = Path(settings.BASE_DIR) / 'ai' / 'models'
        self.model_path.mkdir(parents=True, exist_ok=True)
        self.model = None
        self.patch_size = 32
        self.confidence_threshold = 0.6
        if TENSORFLOW_AVAILABLE:
            self._load_model()

    def _load_model(self):
        """Charge le modèle Ghana téléchargé"""
        model_file = self.model_path / 'ghana_model.h5'
        if not model_file.exists():
            logger.error(f"Modèle {model_file} non trouvé")
            raise FileNotFoundError(f"Modèle Ghana non trouvé à {model_file}")
        try:
            with tf.device('/CPU:0'):
                self.model = tf.keras.models.load_model(str(model_file))
            logger.info("✅ Modèle Ghana chargé avec succès")
        except Exception as e:
            logger.error(f"Erreur chargement modèle: {e}")
            raise

    def _load_and_preprocess_image(self, image_path):
        """Charge et préprocesse l'image"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Impossible de charger l'image")
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            max_size = 1024
            if max(image.shape[:2]) > max_size:
                image = cv2.resize(image, (max_size, max_size), interpolation=cv2.INTER_LANCZOS4)
            image_array = image.astype(np.float32) / 255.0
            return image_array
        except Exception as e:
            logger.error(f"Erreur preprocessing: {e}")
            return None

    def _extract_patches(self, image):
        """Découpe l'image en patches"""
        patches = []
        coordinates = []
        height, width = image.shape[:2]
        step = self.patch_size
        for y in range(0, height - self.patch_size + 1, step):
            for x in range(0, width - self.patch_size + 1, step):
                patch = image[y:y + self.patch_size, x:x + self.patch_size]
                patches.append(patch)
                coordinates.append((x, y))
        return np.array(patches), coordinates

    def _predict_mining_sites(self, patches, coordinates, image_model):
        """Effectue les prédictions"""
        if not patches or not TENSORFLOW_AVAILABLE or self.model is None:
            return self._simulate_predictions(patches, coordinates, image_model)
        try:
            with tf.device('/CPU:0'):
                predictions = self.model.predict(patches, batch_size=32, verbose=0)
            detections = []
            for pred, (x, y) in zip(predictions, coordinates):
                confidence = float(pred[0])
                if confidence > self.confidence_threshold:
                    detection_type = self._classify_detection_type(confidence)
                    detections.append({'x': x, 'y': y, 'confidence': confidence, 'type': detection_type})
            return detections
        except Exception as e:
            logger.error(f"Erreur prédiction: {e}")
            return self._simulate_predictions(patches, coordinates, image_model)

    def _simulate_predictions(self, patches, coordinates, image_model):
        """Simulation basée sur des métriques simples"""
        detections = []
        for patch, (x, y) in zip(patches, coordinates):
            if len(patch.shape) == 3:
                red_mean = np.mean(patch[:, :, 0])
                confidence = max(0, min(1, red_mean / 255.0))  # Simule une détection basée sur la couleur rouge
                if confidence > self.confidence_threshold:
                    detection_type = self._classify_detection_type(confidence)
                    detections.append({'x': x, 'y': y, 'confidence': confidence, 'type': detection_type})
        return detections

    def _classify_detection_type(self, confidence):
        """Classifie le type selon la confiance"""
        if confidence > 0.9:
            return 'MINING_SITE'
        elif confidence > 0.8:
            return 'WATER_POLLUTION'
        else:
            return 'ACCESS_ROAD'

    def _create_detection_model(self, detection, image_model):
        """Crée un DetectionModel (sans GeoDjango pour l'instant)"""
        # Simplification temporaire sans GeoDjango
        return DetectionModel(
            image=image_model,
            detection_type=detection['type'],
            confidence_score=detection['confidence'],
            latitude=0.0,  # À remplacer par une conversion réelle
            longitude=0.0,  # À remplacer par une conversion réelle
            area_hectares=0.5,  # Valeur par défaut
            algorithm_version="Ghana_Earthrise_v3.7_adapted_CI"
        )

    def analyze_image(self, image_model):
        """Analyse une image"""
        try:
            image_path = default_storage.path(image_model.image_file.name)
            image_array = self._load_and_preprocess_image(image_path)
            if image_array is None:
                return []
            patches, coordinates = self._extract_patches(image_array)
            detections = self._predict_mining_sites(patches, coordinates, image_model)
            filtered_detections = [self._create_detection_model(d, image_model) for d in detections]
            return filtered_detections
        except Exception as e:
            logger.error(f"Erreur analyse IA: {e}")
            return []

def analyze_uploaded_image(image_model):
    """Fonction principale pour analyser une image uploadée"""
    detector = GhanaBasedDetector()
    detections = detector.analyze_image(image_model)
    saved_detections = []
    for detection in detections:
        try:
            detection.save()
            saved_detections.append(detection)
            logger.info(f"✅ Détection sauvée: {detection}")
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
    image_model.processing_status = 'ANALYZED' if saved_detections else 'NO_DETECTION'
    image_model.save()
    return saved_detections