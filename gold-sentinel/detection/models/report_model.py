from django.db import models
from django.contrib.gis.db import models as gis_models
from base.models.helpers.date_time_model import DateTimeModel


class DetectionModel(DateTimeModel):
    """AI mining detections with precise coordinates"""

    DETECTION_TYPES = [
        ('MINING_SITE', 'Mining Site'),
        ('WATER_POLLUTION', 'Water Pollution'),
        ('ACCESS_ROAD', 'Access Road'),
        ('EQUIPMENT', 'Mining Equipment'),
    ]

    VALIDATION_STATUS = [
        ('DETECTED', 'Detected'),
        ('VALIDATED', 'Validated'),
        ('FALSE_POSITIVE', 'False Positive'),
        ('CONFIRMED', 'Confirmed'),
    ]

    # Relations
    image = models.ForeignKey('image.ImageModel', on_delete=models.CASCADE)
    region = models.ForeignKey('region.RegionModel', on_delete=models.CASCADE)

    # Precise coordinates (format: "8.0402-2.8")
    latitude = models.FloatField()
    longitude = models.FloatField()
    coordinate_precision = models.CharField(max_length=50)

    # Detection metadata
    detection_type = models.CharField(max_length=30, choices=DETECTION_TYPES)
    confidence_score = models.FloatField(help_text="AI confidence 0.0-1.0")
    area_hectares = models.FloatField(help_text="Estimated area in hectares")

    # Geometry
    zone_geometry = gis_models.PolygonField(srid=4326)
    center_point = gis_models.PointField(srid=4326)

    # AI processing metadata
    algorithm_version = models.CharField(max_length=50, default="Ghana_v3.7")
    processing_time_seconds = models.FloatField(null=True, blank=True)

    # Validation
    validation_status = models.CharField(max_length=20, choices=VALIDATION_STATUS, default='DETECTED')
    validated_by = models.ForeignKey('account.UserModel', on_delete=models.SET_NULL, null=True, blank=True)
    validated_at = models.DateTimeField(null=True, blank=True)

    # Detection timestamp
    detection_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'mining_detections'
        ordering = ['-detection_date']
        indexes = [
            models.Index(fields=['confidence_score']),
            models.Index(fields=['detection_type']),
            models.Index(fields=['region', 'detection_date']),
        ]

    def __str__(self):
        return f"{self.detection_type} - {self.confidence_score:.2f} - {self.coordinate_precision}"