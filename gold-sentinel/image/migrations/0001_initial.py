# Generated by Django 5.1.4 on 2025-05-28 07:41

import django.contrib.gis.db.models.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('region', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImageModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=150)),
                ('capture_date', models.DateField()),
                ('satellite_source', models.CharField(choices=[('SENTINEL2', 'Sentinel-2'), ('LANDSAT8', 'Landsat-8'), ('LANDSAT9', 'Landsat-9'), ('USER_UPLOAD', 'User Upload'), ('TEST_GENERATED', 'Test Generated')], max_length=50)),
                ('cloud_coverage', models.FloatField(help_text='Percentage 0-100')),
                ('resolution', models.FloatField(help_text='Resolution in meters')),
                ('center_lat', models.FloatField()),
                ('center_lon', models.FloatField()),
                ('covered_area', django.contrib.gis.db.models.fields.PolygonField(srid=4326)),
                ('image_file', models.ImageField(upload_to='images/%Y/%m/')),
                ('thumbnail', models.ImageField(blank=True, upload_to='thumbnails/%Y/%m/')),
                ('processing_status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('ANALYZED', 'Analyzed'), ('ERROR', 'Error')], default='PENDING', max_length=20)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='region.regionmodel')),
            ],
            options={
                'db_table': 'satellite_images',
                'ordering': ['-capture_date'],
            },
        ),
    ]
