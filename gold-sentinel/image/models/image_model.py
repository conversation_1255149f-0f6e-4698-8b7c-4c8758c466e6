from django.db import models
from django.contrib.gis.db import models as gis_models
from base.models.helpers.named_date_time_model import NamedDateTimeModel


class ImageModel(NamedDateTimeModel):
    """Satellite images with metadata and geolocation"""

    SATELLITE_SOURCES = [
        ('SENTINEL2', 'Sentinel-2'),
        ('LANDSAT8', 'Landsat-8'),
        ('LANDSAT9', 'Landsat-9'),
        ('USER_UPLOAD', 'User Upload'),
        ('TEST_GENERATED', 'Test Generated'),
    ]

    PROCESSING_STATUS = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('ANALYZED', 'Analyzed'),
        ('ERROR', 'Error'),
    ]

    region = models.ForeignKey('region.RegionModel', on_delete=models.CASCADE)

    # Satellite metadata
    capture_date = models.DateField()
    satellite_source = models.CharField(max_length=50, choices=SATELLITE_SOURCES)
    cloud_coverage = models.FloatField(help_text="Percentage 0-100")
    resolution = models.FloatField(help_text="Resolution in meters")

    # Geolocation
    center_lat = models.FloatField()
    center_lon = models.FloatField()
    covered_area = gis_models.PolygonField(srid=4326)

    # File storage
    image_file = models.ImageField(upload_to='images/%Y/%m/')
    thumbnail = models.ImageField(upload_to='thumbnails/%Y/%m/', blank=True)

    # Processing
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='PENDING')
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'satellite_images'
        ordering = ['-capture_date']