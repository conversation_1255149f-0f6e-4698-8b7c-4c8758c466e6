# Generated by Django 5.1.4 on 2025-05-28 07:41

import django.contrib.gis.db.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RegionModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(choices=[('ZANZAN', 'Zanzan'), ('DENGUELE', 'Den<PERSON><PERSON><PERSON>'), ('BOUNKANI', 'Bounkani'), ('BONDOUKOU', 'Bondoukou')], max_length=50, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('area_km2', models.FloatField()),
                ('geographic_zone', django.contrib.gis.db.models.fields.PolygonField(srid=4326)),
                ('monitored_zones', models.IntegerField(default=0)),
                ('authorized_sites', models.IntegerField(default=0)),
                ('protected_zones', models.IntegerField(default=0)),
            ],
            options={
                'db_table': 'regions',
                'ordering': ['name'],
            },
        ),
    ]
