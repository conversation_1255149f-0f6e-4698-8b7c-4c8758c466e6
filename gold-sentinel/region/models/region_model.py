from django.db import models
from django.contrib.gis.db import models as gis_models
from base.models.helpers.named_date_time_model import NamedDateTimeModel


class RegionModel(NamedDateTimeModel):
    """Administrative regions of Côte d'Ivoire"""

    REGION_CHOICES = [
        ('<PERSON>AN<PERSON><PERSON>', 'Zanzan'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('BONDOUKO<PERSON>', '<PERSON><PERSON><PERSON><PERSON>'),  # Focus zone
    ]

    # Override name field with choices
    name = models.CharField(max_length=50, choices=REGION_CHOICES, unique=True)
    code = models.CharField(max_length=10, unique=True)
    area_km2 = models.FloatField()
    geographic_zone = gis_models.PolygonField(srid=4326)

    # Dashboard statistics (updated by system)
    monitored_zones = models.IntegerField(default=0)
    authorized_sites = models.IntegerField(default=0)
    protected_zones = models.IntegerField(default=0)

    class Meta:
        db_table = 'regions'
        ordering = ['name']