from django.db import models
from base.models.helpers.date_time_model import DateTimeModel
from region.models.region_model import RegionModel
from detection.models.report_model import DetectionModel
from alert.models.alert_model import AlertModel
from account.models.user_model import UserModel

class EventLog(DateTimeModel):
    """
    Enregistre les événements importants du système,
    utilisé pour le suivi et les audits, peut alimenter un flux d'activité.
    """
    EVENT_TYPES = [
        ('DETECTION_CREATED', 'New Detection Created'),
        ('ALERT_GENERATED', 'New Alert Generated'),
        ('ALERT_ACKNOWLEDGED', 'Alert Acknowledged'),
        ('ALERT_RESOLVED', 'Alert Resolved'),
        ('DETECTION_VALIDATED', 'Detection Validated'),
        ('USER_LOGIN', 'User Logged In'),
        ('REPORT_GENERATED', 'Report Generated'),
        ('IMAGE_PROCESSED', 'Image Processed'),
        ('SYSTEM_ERROR', 'System Error'),
    ]

    event_type = models.CharField(max_length=50, choices=EVENT_TYPES,
                                  help_text="Type of system event")
    message = models.TextField(help_text="Detailed description of the event")
    # Liens optionnels vers les objets concernés
    user = models.ForeignKey(UserModel, on_delete=models.SET_NULL,
                             null=True, blank=True, related_name='user_events')
    detection = models.ForeignKey(DetectionModel, on_delete=models.SET_NULL,
                                 null=True, blank=True, related_name='detection_events')
    alert = models.ForeignKey(AlertModel, on_delete=models.SET_NULL,
                              null=True, blank=True, related_name='alert_events')
    region = models.ForeignKey(RegionModel, on_delete=models.SET_NULL,
                              null=True, blank=True, related_name='region_events')

    class Meta:
        db_table = 'event_logs'
        verbose_name = 'Event Log'
        verbose_name_plural = 'Event Logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['user', 'event_type']),
        ]

    def __str__(self):
        return f"[{self.created_at.strftime('%Y-%m-%d %H:%M')}] {self.get_event_type_display()}: {self.message[:50]}..."