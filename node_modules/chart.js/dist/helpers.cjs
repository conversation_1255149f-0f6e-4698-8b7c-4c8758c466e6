/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */
'use strict';

var helpers_dataset = require('./chunks/helpers.dataset.cjs');
require('@kurkle/color');



exports.HALF_PI = helpers_dataset.HALF_PI;
exports.INFINITY = helpers_dataset.INFINITY;
exports.PI = helpers_dataset.PI;
exports.PITAU = helpers_dataset.PITAU;
exports.QUARTER_PI = helpers_dataset.QUARTER_PI;
exports.RAD_PER_DEG = helpers_dataset.RAD_PER_DEG;
exports.TAU = helpers_dataset.TAU;
exports.TWO_THIRDS_PI = helpers_dataset.TWO_THIRDS_PI;
exports._addGrace = helpers_dataset._addGrace;
exports._alignPixel = helpers_dataset._alignPixel;
exports._alignStartEnd = helpers_dataset._alignStartEnd;
exports._angleBetween = helpers_dataset._angleBetween;
exports._angleDiff = helpers_dataset._angleDiff;
exports._arrayUnique = helpers_dataset._arrayUnique;
exports._attachContext = helpers_dataset._attachContext;
exports._bezierCurveTo = helpers_dataset._bezierCurveTo;
exports._bezierInterpolation = helpers_dataset._bezierInterpolation;
exports._boundSegment = helpers_dataset._boundSegment;
exports._boundSegments = helpers_dataset._boundSegments;
exports._capitalize = helpers_dataset._capitalize;
exports._computeSegments = helpers_dataset._computeSegments;
exports._createResolver = helpers_dataset._createResolver;
exports._decimalPlaces = helpers_dataset._decimalPlaces;
exports._deprecated = helpers_dataset._deprecated;
exports._descriptors = helpers_dataset._descriptors;
exports._elementsEqual = helpers_dataset._elementsEqual;
exports._factorize = helpers_dataset._factorize;
exports._filterBetween = helpers_dataset._filterBetween;
exports._getParentNode = helpers_dataset._getParentNode;
exports._getStartAndCountOfVisiblePoints = helpers_dataset._getStartAndCountOfVisiblePoints;
exports._int16Range = helpers_dataset._int16Range;
exports._isBetween = helpers_dataset._isBetween;
exports._isClickEvent = helpers_dataset._isClickEvent;
exports._isDomSupported = helpers_dataset._isDomSupported;
exports._isPointInArea = helpers_dataset._isPointInArea;
exports._limitValue = helpers_dataset._limitValue;
exports._longestText = helpers_dataset._longestText;
exports._lookup = helpers_dataset._lookup;
exports._lookupByKey = helpers_dataset._lookupByKey;
exports._measureText = helpers_dataset._measureText;
exports._merger = helpers_dataset._merger;
exports._mergerIf = helpers_dataset._mergerIf;
exports._normalizeAngle = helpers_dataset._normalizeAngle;
exports._parseObjectDataRadialScale = helpers_dataset._parseObjectDataRadialScale;
exports._pointInLine = helpers_dataset._pointInLine;
exports._readValueToProps = helpers_dataset._readValueToProps;
exports._rlookupByKey = helpers_dataset._rlookupByKey;
exports._scaleRangesChanged = helpers_dataset._scaleRangesChanged;
exports._setMinAndMaxByKey = helpers_dataset._setMinAndMaxByKey;
exports._splitKey = helpers_dataset._splitKey;
exports._steppedInterpolation = helpers_dataset._steppedInterpolation;
exports._steppedLineTo = helpers_dataset._steppedLineTo;
exports._textX = helpers_dataset._textX;
exports._toLeftRightCenter = helpers_dataset._toLeftRightCenter;
exports._updateBezierControlPoints = helpers_dataset._updateBezierControlPoints;
exports.addRoundedRectPath = helpers_dataset.addRoundedRectPath;
exports.almostEquals = helpers_dataset.almostEquals;
exports.almostWhole = helpers_dataset.almostWhole;
exports.callback = helpers_dataset.callback;
exports.clearCanvas = helpers_dataset.clearCanvas;
exports.clipArea = helpers_dataset.clipArea;
exports.clone = helpers_dataset.clone;
exports.color = helpers_dataset.color;
exports.createContext = helpers_dataset.createContext;
exports.debounce = helpers_dataset.debounce;
exports.defined = helpers_dataset.defined;
exports.distanceBetweenPoints = helpers_dataset.distanceBetweenPoints;
exports.drawPoint = helpers_dataset.drawPoint;
exports.drawPointLegend = helpers_dataset.drawPointLegend;
exports.each = helpers_dataset.each;
exports.easingEffects = helpers_dataset.effects;
exports.finiteOrDefault = helpers_dataset.finiteOrDefault;
exports.fontString = helpers_dataset.fontString;
exports.formatNumber = helpers_dataset.formatNumber;
exports.getAngleFromPoint = helpers_dataset.getAngleFromPoint;
exports.getDatasetClipArea = helpers_dataset.getDatasetClipArea;
exports.getHoverColor = helpers_dataset.getHoverColor;
exports.getMaximumSize = helpers_dataset.getMaximumSize;
exports.getRelativePosition = helpers_dataset.getRelativePosition;
exports.getRtlAdapter = helpers_dataset.getRtlAdapter;
exports.getStyle = helpers_dataset.getStyle;
exports.isArray = helpers_dataset.isArray;
exports.isFinite = helpers_dataset.isNumberFinite;
exports.isFunction = helpers_dataset.isFunction;
exports.isNullOrUndef = helpers_dataset.isNullOrUndef;
exports.isNumber = helpers_dataset.isNumber;
exports.isObject = helpers_dataset.isObject;
exports.isPatternOrGradient = helpers_dataset.isPatternOrGradient;
exports.listenArrayEvents = helpers_dataset.listenArrayEvents;
exports.log10 = helpers_dataset.log10;
exports.merge = helpers_dataset.merge;
exports.mergeIf = helpers_dataset.mergeIf;
exports.niceNum = helpers_dataset.niceNum;
exports.noop = helpers_dataset.noop;
exports.overrideTextDirection = helpers_dataset.overrideTextDirection;
exports.readUsedSize = helpers_dataset.readUsedSize;
exports.renderText = helpers_dataset.renderText;
exports.requestAnimFrame = helpers_dataset.requestAnimFrame;
exports.resolve = helpers_dataset.resolve;
exports.resolveObjectKey = helpers_dataset.resolveObjectKey;
exports.restoreTextDirection = helpers_dataset.restoreTextDirection;
exports.retinaScale = helpers_dataset.retinaScale;
exports.setsEqual = helpers_dataset.setsEqual;
exports.sign = helpers_dataset.sign;
exports.splineCurve = helpers_dataset.splineCurve;
exports.splineCurveMonotone = helpers_dataset.splineCurveMonotone;
exports.supportsEventListenerOptions = helpers_dataset.supportsEventListenerOptions;
exports.throttled = helpers_dataset.throttled;
exports.toDegrees = helpers_dataset.toDegrees;
exports.toDimension = helpers_dataset.toDimension;
exports.toFont = helpers_dataset.toFont;
exports.toFontString = helpers_dataset.toFontString;
exports.toLineHeight = helpers_dataset.toLineHeight;
exports.toPadding = helpers_dataset.toPadding;
exports.toPercentage = helpers_dataset.toPercentage;
exports.toRadians = helpers_dataset.toRadians;
exports.toTRBL = helpers_dataset.toTRBL;
exports.toTRBLCorners = helpers_dataset.toTRBLCorners;
exports.uid = helpers_dataset.uid;
exports.unclipArea = helpers_dataset.unclipArea;
exports.unlistenArrayEvents = helpers_dataset.unlistenArrayEvents;
exports.valueOrDefault = helpers_dataset.valueOrDefault;
//# sourceMappingURL=helpers.cjs.map
