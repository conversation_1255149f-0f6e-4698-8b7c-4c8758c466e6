import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import Alerts from './components/Alerts';
import Reports from './components/Reports';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import LoadingSpinner from './components/LoadingSpinner';
import { mockAuth } from './data/mocks';

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Vérification de l'authentification au chargement
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (token && userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (error) {
        console.error('Erreur lors de la récupération des données utilisateur:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
      }
    }

    setLoading(false);
  }, []);

  // Fonction de connexion
  const handleLogin = async (credentials) => {
    try {
      const response = await mockAuth.login(credentials.email, credentials.password);

      if (response.success) {
        setUser(response.user);
        localStorage.setItem('auth_token', response.token);
        localStorage.setItem('user_data', JSON.stringify(response.user));
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: 'Erreur de connexion' };
    }
  };

  // Fonction de déconnexion
  const handleLogout = async () => {
    try {
      await mockAuth.logout();
      setUser(null);
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  // Simulation du timeout de session (30 minutes)
  useEffect(() => {
    if (user) {
      const sessionTimeout = setTimeout(() => {
        handleLogout();
        alert('Votre session a expiré. Veuillez vous reconnecter.');
      }, 30 * 60 * 1000); // 30 minutes

      return () => clearTimeout(sessionTimeout);
    }
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        {!user ? (
          <Routes>
            <Route path="/login" element={<Login onLogin={handleLogin} />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        ) : (
          <div className="flex h-screen">
            {/* Sidebar */}
            <Sidebar
              user={user}
              isOpen={sidebarOpen}
              onToggle={() => setSidebarOpen(!sidebarOpen)}
            />

            {/* Contenu principal */}
            <div className={`flex-1 flex flex-col transition-all duration-300 ${
              sidebarOpen ? 'ml-64' : 'ml-16'
            }`}>
              {/* Navbar */}
              <Navbar
                user={user}
                onLogout={handleLogout}
                onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
              />

              {/* Contenu des pages */}
              <main className="flex-1 overflow-auto p-6">
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard user={user} />} />
                  <Route path="/alerts" element={<Alerts user={user} />} />
                  <Route path="/reports" element={<Reports user={user} />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </main>
            </div>
          </div>
        )}
      </div>
    </Router>
  );
}

export default App;
