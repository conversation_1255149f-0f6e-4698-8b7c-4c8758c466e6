import React, { useState } from 'react';
import {
  ExclamationTriangleIcon,
  EyeIcon,
  CheckCircleIcon,
  XMarkIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { mockAlertes } from '../data/mocks';

const Alerts = ({ user }) => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAlert, setSelectedAlert] = useState(null);

  const filteredAlertes = mockAlertes.filter(alerte => {
    const matchesFilter = selectedFilter === 'all' || alerte.niveau === selectedFilter;
    const matchesSearch = alerte.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alerte.departement.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getNiveauColor = (niveau) => {
    switch (niveau) {
      case 'critique': return 'bg-red-100 text-red-800 border-red-200';
      case 'moyen': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'faible': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatutColor = (statut) => {
    switch (statut) {
      case 'nouveau': return 'bg-blue-100 text-blue-800';
      case 'en_cours': return 'bg-yellow-100 text-yellow-800';
      case 'resolu': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Alertes</h1>
          <p className="text-gray-600 mt-1">
            Gestion des alertes de détection d'orpaillage - {filteredAlertes.length} alerte(s)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4" />
            <span>Filtres avancés</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>Nouvelle alerte</span>
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-2xl font-bold text-gray-900">{mockAlertes.length}</p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-gray-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critiques</p>
              <p className="text-2xl font-bold text-red-600">
                {mockAlertes.filter(a => a.niveau === 'critique').length}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">En cours</p>
              <p className="text-2xl font-bold text-yellow-600">
                {mockAlertes.filter(a => a.statut === 'en_cours').length}
              </p>
            </div>
            <ClockIcon className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Résolues</p>
              <p className="text-2xl font-bold text-green-600">
                {mockAlertes.filter(a => a.statut === 'resolu').length}
              </p>
            </div>
            <CheckCircleIcon className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="card">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher une alerte..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Niveau:</span>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
            >
              <option value="all">Tous</option>
              <option value="critique">Critique</option>
              <option value="moyen">Moyen</option>
              <option value="faible">Faible</option>
            </select>
          </div>
        </div>
      </div>

      {/* Liste des alertes */}
      <div className="space-y-4">
        {filteredAlertes.map((alerte) => (
          <div key={alerte.id} className="card hover:shadow-xl transition-shadow duration-300">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getNiveauColor(alerte.niveau)}`}>
                    {alerte.niveau.toUpperCase()}
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatutColor(alerte.statut)}`}>
                    {alerte.statut.replace('_', ' ').toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-500">#{alerte.id}</span>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {alerte.description}
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-4 w-4" />
                    <span>{alerte.departement}, {alerte.region}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="h-4 w-4" />
                    <span>{formatDate(alerte.date)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Superficie:</span>
                    <span>{alerte.superficie_estimee} ha</span>
                  </div>
                </div>

                <div className="mt-3 flex items-center space-x-4 text-sm">
                  <span className="text-gray-600">
                    <strong>Confiance:</strong> {Math.round(alerte.confidence * 100)}%
                  </span>
                  <span className="text-gray-600">
                    <strong>Agent:</strong> {alerte.agent_responsable}
                  </span>
                </div>
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                  onClick={() => setSelectedAlert(alerte)}
                >
                  <EyeIcon className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                  <CheckCircleIcon className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Actions requises */}
            {alerte.actions_requises && alerte.actions_requises.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-sm font-medium text-gray-700 mb-2">Actions requises:</p>
                <div className="flex flex-wrap gap-2">
                  {alerte.actions_requises.map((action, index) => (
                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                      {action}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredAlertes.length === 0 && (
        <div className="card text-center py-12">
          <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune alerte trouvée</h3>
          <p className="text-gray-600">Aucune alerte ne correspond à vos critères de recherche.</p>
        </div>
      )}

      {/* Modal de détails (placeholder) */}
      {selectedAlert && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">Détails de l'alerte #{selectedAlert.id}</h2>
              <button
                onClick={() => setSelectedAlert(null)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-600">{selectedAlert.description}</p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Région:</span> {selectedAlert.region}
                </div>
                <div>
                  <span className="font-medium">Département:</span> {selectedAlert.departement}
                </div>
                <div>
                  <span className="font-medium">Date:</span> {formatDate(selectedAlert.date)}
                </div>
                <div>
                  <span className="font-medium">Superficie:</span> {selectedAlert.superficie_estimee} ha
                </div>
              </div>
              {selectedAlert.images && selectedAlert.images.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Images satellite:</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedAlert.images.map((image, index) => (
                      <div key={index} className="bg-gray-200 rounded-lg h-32 flex items-center justify-center">
                        <span className="text-gray-500 text-sm">Image {index + 1}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Alerts;
