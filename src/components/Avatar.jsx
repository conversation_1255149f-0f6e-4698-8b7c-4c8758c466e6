import React from 'react';
import { UserIcon } from '@heroicons/react/24/outline';

const Avatar = ({ user, size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  // Photo par défaut - avatar générique professionnel
  const defaultAvatar = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3ClinearGradient id='grad1' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23FF8C00;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2322C55E;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Ccircle cx='50' cy='50' r='50' fill='url(%23grad1)'/%3E%3Ccircle cx='50' cy='35' r='15' fill='white' opacity='0.9'/%3E%3Cpath d='M20 80 Q20 65 35 65 L65 65 Q80 65 80 80 L80 90 Q80 95 75 95 L25 95 Q20 95 20 90 Z' fill='white' opacity='0.9'/%3E%3C/svg%3E";

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      {user.photo ? (
        <img
          src={user.photo}
          alt={`${user.prenoms} ${user.nom}`}
          className={`${sizeClasses[size]} rounded-xl object-cover shadow-lg border-2 border-white`}
        />
      ) : (
        <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-orange-ci-400 to-vert-ci-400 flex items-center justify-center shadow-lg border-2 border-white overflow-hidden`}>
          <img
            src={defaultAvatar}
            alt="Avatar par défaut"
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      {/* Indicateur de statut en ligne */}
      <div className="absolute -bottom-1 -right-1 h-3 w-3 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
    </div>
  );
};

export default Avatar;
