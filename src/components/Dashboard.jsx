import React from 'react';
import {
  ArrowTrendingUpIcon,
  CalendarIcon,
  ClockIcon,
  MapIcon
} from '@heroicons/react/24/outline';

const Dashboard = ({ user }) => {
  // Données pour les graphiques
  const evolutionData = {
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
    datasets: [
      {
        label: 'Détections',
        data: [5, 8, 12, 18, 28],
        borderColor: '#2E7D32',
        backgroundColor: 'rgba(46, 125, 50, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Alertes',
        data: [2, 3, 5, 8, 12],
        borderColor: '#DC2626',
        backgroundColor: 'rgba(220, 38, 38, 0.1)',
        tension: 0.4,
      }
    ],
  };

  const repartitionData = {
    labels: ['Zanzan', 'Denguélé', 'Bounkani'],
    datasets: [
      {
        data: [18, 7, 3],
        backgroundColor: [
          '#2E7D32',
          '#FFD700',
          '#1E40AF',
        ],
        borderWidth: 2,
        borderColor: '#fff',
      },
    ],
  };

  const alertesParTypeData = {
    labels: ['Critique', 'Moyen', 'Faible'],
    datasets: [
      {
        data: [12, 8, 8],
        backgroundColor: [
          '#DC2626',
          '#F59E0B',
          '#10B981',
        ],
        borderWidth: 2,
        borderColor: '#fff',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Tableau de bord - {user.fonction}
          </h1>
          <p className="text-gray-600 mt-1">
            Softéa - Surveillance intelligente de l'orpaillage
            {user.niveau_acces !== 'NATIONAL' && ` - Région ${user.region_autorisee.join(', ')}`}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500 flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            Dernière mise à jour
          </p>
          <p className="text-sm font-medium text-gray-900">
            {new Date().toLocaleString('fr-FR')}
          </p>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Alertes actives</p>
              <p className="text-2xl font-bold text-red-600 mt-1">12</p>
            </div>
            <div className="p-3 rounded-lg bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Zones surveillées</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">45</p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100">
              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Détections ce mois</p>
              <p className="text-2xl font-bold text-primary-600 mt-1">28</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-100">
              <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Sites confirmés</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">8</p>
              <p className="text-sm text-gray-500 mt-1">15.7 hectares</p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100">
              <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Message de bienvenue */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Bienvenue, {user.prenoms} {user.nom}
        </h2>
        <p className="text-gray-600 mb-4">
          Vous êtes connecté en tant que <strong>{user.fonction}</strong> avec un niveau d'accès <strong>{user.niveau_acces}</strong>.
        </p>
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
          <p className="text-blue-700 text-sm">
            <strong>Région(s) autorisée(s):</strong> {user.region_autorisee.join(', ')}
          </p>
        </div>
      </div>

      {/* Graphiques et analyses */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution temporelle */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Évolution des détections</h3>
            <div className="flex items-center space-x-2 text-sm text-green-600">
              <ArrowTrendingUpIcon className="h-4 w-4" />
              <span>+56% ce mois</span>
            </div>
          </div>
          <div className="h-72 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
            <div className="text-center">
              <ArrowTrendingUpIcon className="h-12 w-12 text-green-500 mx-auto mb-2" />
              <p className="text-gray-600 font-medium">Évolution des détections</p>
              <p className="text-gray-500 text-sm">Graphique Chart.js (temporairement désactivé)</p>
              <div className="mt-4 flex justify-center space-x-2">
                <div className="w-2 h-8 bg-green-400 rounded"></div>
                <div className="w-2 h-12 bg-green-500 rounded"></div>
                <div className="w-2 h-16 bg-green-600 rounded"></div>
                <div className="w-2 h-20 bg-green-700 rounded"></div>
                <div className="w-2 h-24 bg-green-800 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Répartition par région */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par région</h3>
          <div className="h-72 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 via-yellow-400 to-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full"></div>
              </div>
              <p className="text-gray-600 font-medium">Répartition par région</p>
              <p className="text-gray-500 text-sm">Graphique en secteurs (temporairement désactivé)</p>
              <div className="mt-4 flex justify-center space-x-4">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Zanzan</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Denguélé</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Bounkani</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Alertes par niveau */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes par niveau de criticité</h3>
          <div className="h-72 bg-gradient-to-br from-red-50 to-pink-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
            <div className="text-center">
              <div className="flex space-x-2 justify-center mb-4">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">12</span>
                </div>
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">8</span>
                </div>
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">8</span>
                </div>
              </div>
              <p className="text-gray-600 font-medium">Alertes par criticité</p>
              <p className="text-gray-500 text-sm">Graphique en secteurs (temporairement désactivé)</p>
              <div className="mt-4 flex justify-center space-x-4">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Critique</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Moyen</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">Faible</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Activité récente */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Nouvelle alerte critique</p>
                <p className="text-sm text-gray-600">Site clandestin détecté près de Bouna</p>
                <p className="text-xs text-gray-500 mt-1">Il y a 2 heures</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Rapport généré</p>
                <p className="text-sm text-gray-600">Rapport mensuel de surveillance</p>
                <p className="text-xs text-gray-500 mt-1">Il y a 4 heures</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Site fermé</p>
                <p className="text-sm text-gray-600">Site illégal de Tanda Est fermé</p>
                <p className="text-xs text-gray-500 mt-1">Hier</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Mise à jour système</p>
                <p className="text-sm text-gray-600">Algorithme IA mis à jour</p>
                <p className="text-xs text-gray-500 mt-1">Il y a 2 jours</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="btn-primary flex items-center justify-center space-x-2 py-3">
            <MapIcon className="h-5 w-5" />
            <span>Voir la carte</span>
          </button>
          <button className="btn-outline flex items-center justify-center space-x-2 py-3">
            <CalendarIcon className="h-5 w-5" />
            <span>Générer rapport</span>
          </button>
          <button className="btn-secondary flex items-center justify-center space-x-2 py-3">
            <ArrowTrendingUpIcon className="h-5 w-5" />
            <span>Analyser tendances</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
