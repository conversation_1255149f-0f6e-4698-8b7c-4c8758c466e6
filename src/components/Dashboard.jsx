import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  ExclamationTriangleIcon,
  MapIcon,
  EyeIcon,
  DocumentTextIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { mockStats, mockAlertes, mockDetections } from '../data/mocks';
import LoadingSpinner from './LoadingSpinner';

const Dashboard = ({ user }) => {
  const [stats, setStats] = useState(null);
  const [recentAlerts, setRecentAlerts] = useState([]);
  const [recentDetections, setRecentDetections] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulation du chargement des données
    const loadDashboardData = async () => {
      setLoading(true);
      
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Filtrer les données selon les permissions utilisateur
      let filteredAlerts = mockAlertes;
      let filteredDetections = mockDetections;
      
      if (user.niveau_acces !== 'NATIONAL') {
        filteredAlerts = mockAlertes.filter(alert => 
          user.region_autorisee.includes(alert.region)
        );
        filteredDetections = mockDetections.filter(detection => 
          user.region_autorisee.includes(detection.region)
        );
      }
      
      setStats(mockStats);
      setRecentAlerts(filteredAlerts.slice(0, 5));
      setRecentDetections(filteredDetections.slice(0, 3));
      setLoading(false);
    };

    loadDashboardData();
  }, [user]);

  const getAlertColor = (niveau) => {
    switch (niveau) {
      case 'critique': return 'bg-red-100 text-red-800 border-red-200';
      case 'moyen': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'faible': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = 'primary' }) => (
    <div className="card">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600 mt-1`}>{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
      </div>
      {trend && (
        <div className="flex items-center mt-3">
          {trend > 0 ? (
            <TrendingUpIcon className="h-4 w-4 text-red-500 mr-1" />
          ) : (
            <TrendingDownIcon className="h-4 w-4 text-green-500 mr-1" />
          )}
          <span className={`text-sm ${trend > 0 ? 'text-red-600' : 'text-green-600'}`}>
            {Math.abs(trend)}% ce mois
          </span>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête du dashboard */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Tableau de bord - {user.fonction}
          </h1>
          <p className="text-gray-600 mt-1">
            Vue d'ensemble de la surveillance d'orpaillage illégal
            {user.niveau_acces !== 'NATIONAL' && ` - Région ${user.region_autorisee.join(', ')}`}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Dernière mise à jour</p>
          <p className="text-sm font-medium text-gray-900">
            {new Date().toLocaleString('fr-FR')}
          </p>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Alertes actives"
          value={stats.alertes_actives}
          icon={ExclamationTriangleIcon}
          color="red"
          trend={15}
        />
        <StatCard
          title="Zones surveillées"
          value={stats.zones_surveillees}
          icon={MapIcon}
          color="blue"
        />
        <StatCard
          title="Détections ce mois"
          value={stats.detections_mois}
          icon={EyeIcon}
          color="primary"
          trend={8}
        />
        <StatCard
          title="Sites confirmés"
          value={stats.sites_confirmes}
          subtitle={`${stats.superficie_affectee} hectares`}
          icon={DocumentTextIcon}
          color="yellow"
          trend={-5}
        />
      </div>

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alertes récentes */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Alertes récentes</h2>
              <Link
                to="/alerts"
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Voir toutes →
              </Link>
            </div>
            <div className="space-y-3">
              {recentAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      alert.niveau === 'critique' ? 'bg-red-500' :
                      alert.niveau === 'moyen' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {alert.description}
                      </p>
                      <p className="text-xs text-gray-500">
                        {alert.region} • {new Date(alert.date).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                    getAlertColor(alert.niveau)
                  }`}>
                    {alert.niveau}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Activité récente */}
        <div className="space-y-6">
          {/* Détections récentes */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Détections récentes</h2>
              <Link
                to="/map"
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Voir carte →
              </Link>
            </div>
            <div className="space-y-3">
              {recentDetections.map((detection) => (
                <div key={detection.id} className="border-l-4 border-primary-500 pl-3">
                  <p className="text-sm font-medium text-gray-900">
                    {detection.region}
                  </p>
                  <p className="text-xs text-gray-500">
                    {detection.satellite} • {new Date(detection.date_acquisition).toLocaleDateString('fr-FR')}
                  </p>
                  <div className="mt-1">
                    {detection.changements_detectes.map((changement, index) => (
                      <span
                        key={index}
                        className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded mr-1"
                      >
                        {changement.type}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions rapides */}
          <div className="card">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h2>
            <div className="space-y-2">
              <Link
                to="/map"
                className="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              >
                <MapIcon className="h-4 w-4 mr-2 text-primary-500" />
                Analyser une nouvelle zone
              </Link>
              <Link
                to="/reports"
                className="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              >
                <DocumentTextIcon className="h-4 w-4 mr-2 text-primary-500" />
                Générer un rapport
              </Link>
              <Link
                to="/history"
                className="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              >
                <ClockIcon className="h-4 w-4 mr-2 text-primary-500" />
                Consulter l'historique
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Graphique d'évolution (placeholder) */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Évolution des détections</h2>
        <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center text-gray-500">
            <TrendingUpIcon className="h-12 w-12 mx-auto mb-2" />
            <p>Graphique d'évolution des détections</p>
            <p className="text-sm">(Intégration Chart.js à venir)</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
