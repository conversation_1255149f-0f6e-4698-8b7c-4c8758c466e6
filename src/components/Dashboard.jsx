import React from 'react';

const Dashboard = ({ user }) => {
  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Tableau de bord - {user.fonction}
          </h1>
          <p className="text-gray-600 mt-1">
            Vue d'ensemble de la surveillance d'orpaillage illégal
            {user.niveau_acces !== 'NATIONAL' && ` - Région ${user.region_autorisee.join(', ')}`}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Dernière mise à jour</p>
          <p className="text-sm font-medium text-gray-900">
            {new Date().toLocaleString('fr-FR')}
          </p>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Alertes actives</p>
              <p className="text-2xl font-bold text-red-600 mt-1">12</p>
            </div>
            <div className="p-3 rounded-lg bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Zones surveillées</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">45</p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100">
              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Détections ce mois</p>
              <p className="text-2xl font-bold text-primary-600 mt-1">28</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-100">
              <svg className="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Sites confirmés</p>
              <p className="text-2xl font-bold text-yellow-600 mt-1">8</p>
              <p className="text-sm text-gray-500 mt-1">15.7 hectares</p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100">
              <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Message de bienvenue */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Bienvenue, {user.prenoms} {user.nom}
        </h2>
        <p className="text-gray-600 mb-4">
          Vous êtes connecté en tant que <strong>{user.fonction}</strong> avec un niveau d'accès <strong>{user.niveau_acces}</strong>.
        </p>
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
          <p className="text-blue-700 text-sm">
            <strong>Région(s) autorisée(s):</strong> {user.region_autorisee.join(', ')}
          </p>
        </div>
      </div>

      {/* Placeholder pour les autres composants */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes récentes</h3>
          <p className="text-gray-500">Composant en cours de développement...</p>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
          <p className="text-gray-500">Composant en cours de développement...</p>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
