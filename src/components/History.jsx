import React, { useState } from 'react';
import {
  ClockIcon,
  EyeIcon,
  UserIcon,
  ComputerDesktopIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { mockAuditLogs } from '../data/mocks';

const History = ({ user }) => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  // Données d'historique étendues
  const historyData = [
    ...mockAuditLogs,
    {
      id: 4,
      timestamp: "2025-05-27T13:45:00Z",
      utilisateur: "<EMAIL>",
      action: "VALIDATION_ALERTE",
      ressource: "Alerte #2 - Site Tanda Est",
      ip_address: "*************",
      user_agent: "Mozilla/5.0...",
      statut: "SUCCESS",
      details: "Alerte validée et classée comme site confirmé"
    },
    {
      id: 5,
      timestamp: "2025-05-27T13:30:00Z",
      utilisateur: "<EMAIL>",
      action: "MISSION_TERRAIN",
      ressource: "Mission Bouna Nord",
      ip_address: "*************",
      user_agent: "Mobile App",
      statut: "SUCCESS",
      details: "Mission de vérification terrain terminée"
    },
    {
      id: 6,
      timestamp: "2025-05-27T12:15:00Z",
      utilisateur: "<EMAIL>",
      action: "CREATION_RAPPORT",
      ressource: "Rapport hebdomadaire",
      ip_address: "*************",
      user_agent: "Mozilla/5.0...",
      statut: "SUCCESS",
      details: "Rapport généré avec succès"
    },
    {
      id: 7,
      timestamp: "2025-05-27T11:00:00Z",
      utilisateur: "<EMAIL>",
      action: "MODIFICATION_PARAMETRES",
      ressource: "Seuils IA",
      ip_address: "*************",
      user_agent: "Mozilla/5.0...",
      statut: "SUCCESS",
      details: "Seuils de détection mis à jour"
    },
    {
      id: 8,
      timestamp: "2025-05-27T10:30:00Z",
      utilisateur: "<EMAIL>",
      action: "DETECTION_AUTOMATIQUE",
      ressource: "Analyse satellite quotidienne",
      ip_address: "127.0.0.1",
      user_agent: "System",
      statut: "SUCCESS",
      details: "3 nouvelles détections identifiées"
    }
  ];

  const filteredHistory = historyData.filter(item => {
    const matchesFilter = selectedFilter === 'all' || item.action.toLowerCase().includes(selectedFilter.toLowerCase());
    const matchesSearch = item.utilisateur.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.ressource.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getActionIcon = (action) => {
    switch (action) {
      case 'LOGIN': return UserIcon;
      case 'CONSULTATION_ALERTE': return EyeIcon;
      case 'EXPORT_RAPPORT': return DocumentTextIcon;
      case 'VALIDATION_ALERTE': return CheckCircleIcon;
      case 'MISSION_TERRAIN': return ComputerDesktopIcon;
      case 'CREATION_RAPPORT': return DocumentTextIcon;
      case 'MODIFICATION_PARAMETRES': return ComputerDesktopIcon;
      case 'DETECTION_AUTOMATIQUE': return ExclamationTriangleIcon;
      default: return ClockIcon;
    }
  };

  const getActionColor = (action) => {
    switch (action) {
      case 'LOGIN': return 'bg-blue-100 text-blue-600';
      case 'CONSULTATION_ALERTE': return 'bg-green-100 text-green-600';
      case 'EXPORT_RAPPORT': return 'bg-purple-100 text-purple-600';
      case 'VALIDATION_ALERTE': return 'bg-green-100 text-green-600';
      case 'MISSION_TERRAIN': return 'bg-orange-100 text-orange-600';
      case 'CREATION_RAPPORT': return 'bg-blue-100 text-blue-600';
      case 'MODIFICATION_PARAMETRES': return 'bg-yellow-100 text-yellow-600';
      case 'DETECTION_AUTOMATIQUE': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionLabel = (action) => {
    const labels = {
      'LOGIN': 'Connexion',
      'CONSULTATION_ALERTE': 'Consultation alerte',
      'EXPORT_RAPPORT': 'Export rapport',
      'VALIDATION_ALERTE': 'Validation alerte',
      'MISSION_TERRAIN': 'Mission terrain',
      'CREATION_RAPPORT': 'Création rapport',
      'MODIFICATION_PARAMETRES': 'Modification paramètres',
      'DETECTION_AUTOMATIQUE': 'Détection automatique'
    };
    return labels[action] || action;
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Historique</h1>
          <p className="text-gray-600 mt-1">
            Historique des actions et détections - {filteredHistory.length} entrée(s)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4" />
            <span>Filtres avancés</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <DocumentTextIcon className="h-4 w-4" />
            <span>Exporter historique</span>
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Actions aujourd'hui</p>
              <p className="text-2xl font-bold text-blue-600">
                {historyData.filter(item =>
                  new Date(item.timestamp).toDateString() === new Date().toDateString()
                ).length}
              </p>
            </div>
            <ClockIcon className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Connexions</p>
              <p className="text-2xl font-bold text-green-600">
                {historyData.filter(item => item.action === 'LOGIN').length}
              </p>
            </div>
            <UserIcon className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Détections auto</p>
              <p className="text-2xl font-bold text-red-600">
                {historyData.filter(item => item.action === 'DETECTION_AUTOMATIQUE').length}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rapports générés</p>
              <p className="text-2xl font-bold text-purple-600">
                {historyData.filter(item => item.action.includes('RAPPORT')).length}
              </p>
            </div>
            <DocumentTextIcon className="h-8 w-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Filtres */}
      <div className="card">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher dans l'historique..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Action:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
              >
                <option value="all">Toutes</option>
                <option value="login">Connexions</option>
                <option value="alerte">Alertes</option>
                <option value="rapport">Rapports</option>
                <option value="detection">Détections</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Période:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
              >
                <option value="day">Aujourd'hui</option>
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="year">Cette année</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Timeline des actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Timeline des actions</h3>
        <div className="space-y-4">
          {filteredHistory.map((item, index) => {
            const ActionIcon = getActionIcon(item.action);
            return (
              <div key={item.id} className="flex items-start space-x-4 p-4 hover:bg-gray-50 rounded-lg transition-colors">
                <div className={`p-2 rounded-lg ${getActionColor(item.action)}`}>
                  <ActionIcon className="h-5 w-5" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      {getActionLabel(item.action)}
                    </h4>
                    <span className="text-xs text-gray-500">
                      {formatDate(item.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    <strong>Utilisateur:</strong> {item.utilisateur}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Ressource:</strong> {item.ressource}
                  </p>
                  {item.details && (
                    <p className="text-sm text-gray-500 mt-1 italic">
                      {item.details}
                    </p>
                  )}
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                    <span>IP: {item.ip_address}</span>
                    <span className={`px-2 py-1 rounded-full ${
                      item.statut === 'SUCCESS' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.statut}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredHistory.length === 0 && (
          <div className="text-center py-12">
            <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun historique trouvé</h3>
            <p className="text-gray-600">Aucune action ne correspond à vos critères de recherche.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
