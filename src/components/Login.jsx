import React, { useState } from 'react';
import { EyeIcon, EyeSlashIcon, ShieldCheckIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from './LoadingSpinner';
import { mockAuth } from '../data/mocks';

const Login = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [show2FA, setShow2FA] = useState(false);
  const [otpCode, setOtpCode] = useState('');
  const [otpLoading, setOtpLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await onLogin(formData);

      if (result.success) {
        // Simuler l'envoi d'un code 2FA
        setShow2FA(true);
        setLoading(false);
      } else {
        setError(result.error || 'Erreur de connexion');
        setLoading(false);
      }
    } catch (error) {
      setError('Erreur de connexion au serveur');
      setLoading(false);
    }
  };

  const handleOTPSubmit = async (e) => {
    e.preventDefault();
    setOtpLoading(true);
    setError('');

    try {
      const result = await mockAuth.verify2FA(otpCode);

      if (result.success) {
        // L'authentification est complète, l'utilisateur sera redirigé par App.jsx
        setOtpLoading(false);
      } else {
        setError(result.error || 'Code de vérification invalide');
        setOtpLoading(false);
      }
    } catch (error) {
      setError('Erreur lors de la vérification');
      setOtpLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'password123'
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-government-blue to-government-darkBlue py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* En-tête */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center mb-4">
            <ShieldCheckIcon className="h-10 w-10 text-government-blue" />
          </div>
          <h2 className="text-3xl font-bold text-white mb-2">
            Softéa
          </h2>
          <p className="text-blue-100 text-sm">
            Surveillance intelligente de l'orpaillage
          </p>
          <p className="text-blue-200 text-xs mt-1">
            République de Côte d'Ivoire - Ministère des Mines
          </p>
        </div>

        {/* Formulaire de connexion */}
        {!show2FA ? (
          <div className="bg-white rounded-xl shadow-2xl p-8 space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Connexion sécurisée</h3>
              <p className="text-gray-600 text-sm">Accès réservé au personnel autorisé</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <div className="alert-critical">
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Adresse email professionnelle
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  className="input-field"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Mot de passe
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    className="input-field pr-10"
                    placeholder="••••••••"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full btn-primary flex items-center justify-center"
              >
                {loading ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  'Se connecter'
                )}
              </button>
            </form>

            {/* Bouton de démonstration */}
            <div className="border-t pt-4">
              <button
                type="button"
                onClick={handleDemoLogin}
                className="w-full btn-outline text-sm"
              >
                Utiliser les identifiants de démonstration
              </button>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Email: <EMAIL> | Mot de passe: password123
              </p>
            </div>

            <div className="text-center">
              <a href="#" className="text-sm text-government-blue hover:text-government-darkBlue">
                Mot de passe oublié ?
              </a>
            </div>
          </div>
        ) : (
          /* Formulaire 2FA */
          <div className="bg-white rounded-xl shadow-2xl p-8 space-y-6">
            <div className="text-center">
              <DevicePhoneMobileIcon className="mx-auto h-12 w-12 text-government-blue mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Vérification en deux étapes</h3>
              <p className="text-gray-600 text-sm">
                Un code de vérification a été envoyé par SMS au numéro enregistré
              </p>
            </div>

            <form onSubmit={handleOTPSubmit} className="space-y-4">
              {error && (
                <div className="alert-critical">
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
              )}

              <div>
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-1">
                  Code de vérification (6 chiffres)
                </label>
                <input
                  id="otp"
                  name="otp"
                  type="text"
                  maxLength="6"
                  required
                  className="input-field text-center text-lg tracking-widest"
                  placeholder="123456"
                  value={otpCode}
                  onChange={(e) => setOtpCode(e.target.value.replace(/\D/g, ''))}
                />
              </div>

              <button
                type="submit"
                disabled={otpLoading || otpCode.length !== 6}
                className="w-full btn-primary flex items-center justify-center"
              >
                {otpLoading ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  'Vérifier le code'
                )}
              </button>
            </form>

            <div className="text-center">
              <button
                type="button"
                onClick={() => setShow2FA(false)}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                ← Retour à la connexion
              </button>
            </div>

            <div className="alert-info">
              <p className="text-blue-700 text-xs">
                <strong>Démonstration:</strong> Utilisez le code <strong>123456</strong> pour continuer
              </p>
            </div>
          </div>
        )}

        {/* Informations de sécurité */}
        <div className="text-center text-blue-100 text-xs space-y-1">
          <p>🔒 Connexion sécurisée SSL/TLS</p>
          <p>📱 Authentification à deux facteurs requise</p>
          <p>🕐 Session automatiquement fermée après 30 minutes d'inactivité</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
