import React, { useState, useEffect } from 'react';
import {
  MapIcon,
  MagnifyingGlassIcon,
  PhotoIcon,
  PlayIcon,
  DocumentArrowDownIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { mockRegions, mockDetections, mockAlertes } from '../data/mocks';
import LoadingSpinner from './LoadingSpinner';

const MapView = ({ user }) => {
  const [selectedRegion, setSelectedRegion] = useState('zanzan');
  const [selectedCoordinates, setSelectedCoordinates] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showImageComparison, setShowImageComparison] = useState(false);
  const [mapMode, setMapMode] = useState('satellite'); // satellite, terrain, hybrid

  // Coordonnées par défaut pour Bondoukou
  const defaultCenter = { lat: 8.0402, lng: -2.8000 };

  useEffect(() => {
    // Initialiser avec la région Zanzan si l'utilisateur y a accès
    if (user.region_autorisee.includes('zanzan') || user.region_autorisee.includes('ALL')) {
      setSelectedRegion('zanzan');
    } else if (user.region_autorisee.length > 0) {
      setSelectedRegion(user.region_autorisee[0].toLowerCase());
    }
  }, [user]);

  const handleMapClick = (coordinates) => {
    setSelectedCoordinates(coordinates);
    setAnalysisResults(null);
    setShowImageComparison(false);
  };

  const runAnalysis = async () => {
    if (!selectedCoordinates) return;

    setLoading(true);

    // Simuler l'analyse IA
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Résultats mockés
    const mockResults = {
      confidence: 0.87,
      changements_detectes: [
        {
          type: 'Déforestation',
          superficie: 2.3,
          confidence: 0.92,
          description: 'Zone de déforestation récente détectée'
        },
        {
          type: 'Excavation',
          superficie: 0.8,
          confidence: 0.85,
          description: 'Activité d\'excavation suspecte'
        }
      ],
      risque_global: 'Élevé',
      recommandations: [
        'Vérification terrain recommandée',
        'Surveillance continue nécessaire',
        'Alerte aux autorités locales'
      ]
    };

    setAnalysisResults(mockResults);
    setShowImageComparison(true);
    setLoading(false);
  };

  const exportResults = () => {
    // Simulation d'export
    const data = {
      coordinates: selectedCoordinates,
      analysis: analysisResults,
      timestamp: new Date().toISOString(),
      user: user.email
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analyse_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const availableRegions = Object.entries(mockRegions).filter(([key]) =>
    user.region_autorisee.includes('ALL') ||
    user.region_autorisee.map(r => r.toLowerCase()).includes(key)
  );

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Carte interactive</h1>
          <p className="text-gray-600 mt-1">
            Analyse satellite et détection d'activités d'orpaillage
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={mapMode}
            onChange={(e) => setMapMode(e.target.value)}
            className="input-field w-auto"
          >
            <option value="satellite">Vue satellite</option>
            <option value="terrain">Vue terrain</option>
            <option value="hybrid">Vue hybride</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          {/* Sélection de région */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Sélection de zone</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Région
                </label>
                <select
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                  className="input-field"
                >
                  {availableRegions.map(([key, region]) => (
                    <option key={key} value={key}>
                      {region.nom}
                    </option>
                  ))}
                </select>
              </div>

              {selectedCoordinates && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-900">Zone sélectionnée</p>
                  <p className="text-xs text-blue-700">
                    Lat: {selectedCoordinates.lat.toFixed(4)}
                  </p>
                  <p className="text-xs text-blue-700">
                    Lng: {selectedCoordinates.lng.toFixed(4)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Actions</h3>
            <div className="space-y-2">
              <button
                onClick={runAnalysis}
                disabled={!selectedCoordinates || loading}
                className="w-full btn-primary flex items-center justify-center"
              >
                {loading ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-2" />
                    Lancer l'analyse
                  </>
                )}
              </button>

              <button
                onClick={() => setShowImageComparison(!showImageComparison)}
                disabled={!analysisResults}
                className="w-full btn-outline flex items-center justify-center"
              >
                <PhotoIcon className="h-4 w-4 mr-2" />
                Comparer images
              </button>

              <button
                onClick={exportResults}
                disabled={!analysisResults}
                className="w-full btn-secondary flex items-center justify-center"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Exporter résultats
              </button>
            </div>
          </div>

          {/* Paramètres d'analyse */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Paramètres</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Seuil de détection
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="1"
                  step="0.05"
                  defaultValue="0.75"
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Sensible</span>
                  <span>Précis</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Types de changements
                </label>
                <div className="space-y-1">
                  {['Déforestation', 'Excavation', 'Construction'].map((type) => (
                    <label key={type} className="flex items-center">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{type}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Carte principale */}
        <div className="lg:col-span-3 space-y-4">
          {/* Carte simulée */}
          <div className="card p-0 overflow-hidden">
            <div className="h-96 bg-gradient-to-br from-green-100 to-blue-100 relative">
              {/* Simulation d'une carte */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <MapIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 font-medium">Carte interactive Leaflet</p>
                  <p className="text-gray-500 text-sm">
                    Cliquez sur une zone pour sélectionner les coordonnées
                  </p>
                  {selectedRegion && mockRegions[selectedRegion] && (
                    <div className="mt-4 p-3 bg-white rounded-lg shadow-md inline-block">
                      <p className="font-medium text-gray-900">
                        {mockRegions[selectedRegion].nom}
                      </p>
                      <p className="text-sm text-gray-600">
                        {mockRegions[selectedRegion].departements.join(', ')}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Simulation de points d'alerte */}
              <div className="absolute top-4 left-4 space-y-2">
                {mockAlertes.slice(0, 3).map((alert) => (
                  <div
                    key={alert.id}
                    className={`w-3 h-3 rounded-full animate-pulse ${
                      alert.niveau === 'critique' ? 'bg-red-500' :
                      alert.niveau === 'moyen' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    title={alert.description}
                  ></div>
                ))}
              </div>

              {/* Bouton de simulation de clic */}
              <button
                onClick={() => handleMapClick({ lat: 8.0402 + Math.random() * 0.1, lng: -2.8000 + Math.random() * 0.1 })}
                className="absolute bottom-4 right-4 btn-primary"
              >
                Simuler sélection
              </button>
            </div>
          </div>

          {/* Résultats d'analyse */}
          {analysisResults && (
            <div className="card animate-slide-up">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Résultats d'analyse</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-600 font-medium">Confiance globale</p>
                  <p className="text-2xl font-bold text-blue-900">
                    {(analysisResults.confidence * 100).toFixed(0)}%
                  </p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-yellow-600 font-medium">Risque</p>
                  <p className="text-2xl font-bold text-yellow-900">
                    {analysisResults.risque_global}
                  </p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-600 font-medium">Changements</p>
                  <p className="text-2xl font-bold text-green-900">
                    {analysisResults.changements_detectes.length}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Changements détectés</h4>
                {analysisResults.changements_detectes.map((changement, index) => (
                  <div key={index} className="border-l-4 border-primary-500 pl-3 py-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">{changement.type}</p>
                        <p className="text-sm text-gray-600">{changement.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {changement.superficie} ha
                        </p>
                        <p className="text-xs text-gray-500">
                          {(changement.confidence * 100).toFixed(0)}% confiance
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Recommandations</h4>
                <ul className="space-y-1">
                  {analysisResults.recommandations.map((rec, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-center">
                      <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-2"></span>
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Comparaison d'images */}
          {showImageComparison && (
            <div className="card animate-slide-up">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Comparaison d'images</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Avant (6 mois)</h4>
                  <div className="aspect-video bg-green-200 rounded-lg flex items-center justify-center">
                    <div className="text-center text-green-700">
                      <PhotoIcon className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Image Sentinel-2</p>
                      <p className="text-xs">Zone forestière intacte</p>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Après (actuel)</h4>
                  <div className="aspect-video bg-yellow-200 rounded-lg flex items-center justify-center">
                    <div className="text-center text-yellow-700">
                      <PhotoIcon className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Image Sentinel-2</p>
                      <p className="text-xs">Activité détectée</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MapView;
