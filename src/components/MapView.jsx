import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, Circle, LayersControl } from 'react-leaflet';
import {
  ExclamationTriangleIcon,
  EyeIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix pour les icônes Leaflet avec Vite
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Icônes personnalisées pour les alertes
const alertIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const siteIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-orange.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const MapView = ({ user }) => {
  const [selectedLayer, setSelectedLayer] = useState('satellite');
  const [showAlerts, setShowAlerts] = useState(true);
  const [showSites, setShowSites] = useState(true);
  const [showZones, setShowZones] = useState(true);

  // Coordonnées de Bondoukou (centre de la région Zanzan)
  const bondoukouCenter = [8.0402, -2.8000];

  // Données mockées pour les alertes et sites
  const alertes = [
    {
      id: 1,
      position: [8.0402, -2.8000],
      niveau: 'critique',
      description: 'Site clandestin détecté - activité intense',
      date: '2025-05-27',
      superficie: '2.5 ha'
    },
    {
      id: 2,
      position: [7.8000, -3.1667],
      niveau: 'moyen',
      description: 'Expansion possible d\'un site existant',
      date: '2025-05-27',
      superficie: '1.2 ha'
    },
    {
      id: 3,
      position: [8.1000, -2.7500],
      niveau: 'faible',
      description: 'Activité suspecte détectée',
      date: '2025-05-26',
      superficie: '0.8 ha'
    }
  ];

  const sitesConnus = [
    {
      id: 1,
      position: [8.2000, -2.9000],
      nom: 'Site Bouna Nord',
      statut: 'surveillé',
      superficie: '5.2 ha'
    },
    {
      id: 2,
      position: [7.9000, -3.0000],
      nom: 'Site Tanda Est',
      statut: 'fermé',
      superficie: '3.1 ha'
    }
  ];

  const zonesProtegees = [
    {
      id: 1,
      center: [8.0000, -2.8500],
      radius: 5000,
      nom: 'Zone protégée Bondoukou',
      type: 'Réserve forestière'
    },
    {
      id: 2,
      center: [7.8500, -3.1000],
      radius: 3000,
      nom: 'Zone protégée Tanda',
      type: 'Parc national'
    }
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Carte interactive</h1>
          <p className="text-gray-600 mt-1">
            Surveillance satellite de la région {user.region_autorisee.includes('ALL') ? 'nationale' : user.region_autorisee.join(', ')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <MagnifyingGlassIcon className="h-4 w-4" />
            <span>Rechercher</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <AdjustmentsHorizontalIcon className="h-4 w-4" />
            <span>Filtres</span>
          </button>
        </div>
      </div>

      {/* Contrôles de la carte */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contrôles</h3>

            {/* Couches */}
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Couches</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showAlerts}
                      onChange={(e) => setShowAlerts(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Alertes</span>
                    <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                      {alertes.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showSites}
                      onChange={(e) => setShowSites(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Sites connus</span>
                    <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                      {sitesConnus.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showZones}
                      onChange={(e) => setShowZones(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Zones protégées</span>
                    <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      {zonesProtegees.length}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Légende */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Légende</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Alerte critique</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Site connu</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded-full opacity-30"></div>
                <span className="text-sm text-gray-700">Zone protégée</span>
              </div>
            </div>
          </div>

          {/* Statistiques rapides */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Alertes actives</span>
                <span className="text-sm font-medium text-red-600">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sites surveillés</span>
                <span className="text-sm font-medium text-orange-600">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Superficie totale</span>
                <span className="text-sm font-medium text-gray-900">12.8 ha</span>
              </div>
            </div>
          </div>
        </div>

        {/* Carte */}
        <div className="lg:col-span-3">
          <div className="card p-0 overflow-hidden">
            <div style={{ height: '600px', width: '100%' }}>
              <MapContainer
                center={bondoukouCenter}
                zoom={10}
                style={{ height: '100%', width: '100%' }}
              >
                <LayersControl position="topright">
                  <LayersControl.BaseLayer checked name="Satellite">
                    <TileLayer
                      url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                      attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
                    />
                  </LayersControl.BaseLayer>
                  <LayersControl.BaseLayer name="Carte">
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                    />
                  </LayersControl.BaseLayer>
                </LayersControl>

                {/* Alertes */}
                {showAlerts && alertes.map((alerte) => (
                  <Marker key={alerte.id} position={alerte.position} icon={alertIcon}>
                    <Popup>
                      <div className="p-2">
                        <div className="flex items-center space-x-2 mb-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                          <span className="font-semibold text-red-700">Alerte {alerte.niveau}</span>
                        </div>
                        <p className="text-sm text-gray-700 mb-2">{alerte.description}</p>
                        <div className="text-xs text-gray-500 space-y-1">
                          <div>Date: {alerte.date}</div>
                          <div>Superficie: {alerte.superficie}</div>
                        </div>
                        <button className="mt-2 text-xs bg-primary-500 text-white px-2 py-1 rounded">
                          Voir détails
                        </button>
                      </div>
                    </Popup>
                  </Marker>
                ))}

                {/* Sites connus */}
                {showSites && sitesConnus.map((site) => (
                  <Marker key={site.id} position={site.position} icon={siteIcon}>
                    <Popup>
                      <div className="p-2">
                        <div className="flex items-center space-x-2 mb-2">
                          <MapPinIcon className="h-5 w-5 text-orange-500" />
                          <span className="font-semibold text-orange-700">{site.nom}</span>
                        </div>
                        <div className="text-xs text-gray-500 space-y-1">
                          <div>Statut: {site.statut}</div>
                          <div>Superficie: {site.superficie}</div>
                        </div>
                        <button className="mt-2 text-xs bg-orange-500 text-white px-2 py-1 rounded">
                          Historique
                        </button>
                      </div>
                    </Popup>
                  </Marker>
                ))}

                {/* Zones protégées */}
                {showZones && zonesProtegees.map((zone) => (
                  <Circle
                    key={zone.id}
                    center={zone.center}
                    radius={zone.radius}
                    pathOptions={{
                      color: '#10B981',
                      fillColor: '#10B981',
                      fillOpacity: 0.1,
                      weight: 2
                    }}
                  >
                    <Popup>
                      <div className="p-2">
                        <div className="flex items-center space-x-2 mb-2">
                          <EyeIcon className="h-5 w-5 text-green-500" />
                          <span className="font-semibold text-green-700">{zone.nom}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          Type: {zone.type}
                        </div>
                      </div>
                    </Popup>
                  </Circle>
                ))}
              </MapContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
