import React, { useState, useEffect } from 'react';
import {
  ExclamationTriangleIcon,
  EyeIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Placeholder pour Leaflet - sera réintégré plus tard

const MapView = ({ user }) => {
  const [selectedLayer, setSelectedLayer] = useState('satellite');
  const [showAlerts, setShowAlerts] = useState(true);
  const [showSites, setShowSites] = useState(true);
  const [showZones, setShowZones] = useState(true);

  // Coordonnées de Bondoukou (centre de la région Zanzan)
  const bondoukouCenter = [8.0402, -2.8000];

  // Données mockées pour les alertes et sites
  const alertes = [
    {
      id: 1,
      position: [8.0402, -2.8000],
      niveau: 'critique',
      description: 'Site clandestin détecté - activité intense',
      date: '2025-05-27',
      superficie: '2.5 ha'
    },
    {
      id: 2,
      position: [7.8000, -3.1667],
      niveau: 'moyen',
      description: 'Expansion possible d\'un site existant',
      date: '2025-05-27',
      superficie: '1.2 ha'
    },
    {
      id: 3,
      position: [8.1000, -2.7500],
      niveau: 'faible',
      description: 'Activité suspecte détectée',
      date: '2025-05-26',
      superficie: '0.8 ha'
    }
  ];

  const sitesConnus = [
    {
      id: 1,
      position: [8.2000, -2.9000],
      nom: 'Site Bouna Nord',
      statut: 'surveillé',
      superficie: '5.2 ha'
    },
    {
      id: 2,
      position: [7.9000, -3.0000],
      nom: 'Site Tanda Est',
      statut: 'fermé',
      superficie: '3.1 ha'
    }
  ];

  const zonesProtegees = [
    {
      id: 1,
      center: [8.0000, -2.8500],
      radius: 5000,
      nom: 'Zone protégée Bondoukou',
      type: 'Réserve forestière'
    },
    {
      id: 2,
      center: [7.8500, -3.1000],
      radius: 3000,
      nom: 'Zone protégée Tanda',
      type: 'Parc national'
    }
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Carte interactive</h1>
          <p className="text-gray-600 mt-1">
            Surveillance satellite de la région {user.region_autorisee.includes('ALL') ? 'nationale' : user.region_autorisee.join(', ')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <MagnifyingGlassIcon className="h-4 w-4" />
            <span>Rechercher</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <AdjustmentsHorizontalIcon className="h-4 w-4" />
            <span>Filtres</span>
          </button>
        </div>
      </div>

      {/* Contrôles de la carte */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contrôles</h3>

            {/* Couches */}
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Couches</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showAlerts}
                      onChange={(e) => setShowAlerts(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Alertes</span>
                    <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                      {alertes.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showSites}
                      onChange={(e) => setShowSites(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Sites connus</span>
                    <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                      {sitesConnus.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showZones}
                      onChange={(e) => setShowZones(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Zones protégées</span>
                    <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      {zonesProtegees.length}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Légende */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Légende</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Alerte critique</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Site connu</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded-full opacity-30"></div>
                <span className="text-sm text-gray-700">Zone protégée</span>
              </div>
            </div>
          </div>

          {/* Statistiques rapides */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Alertes actives</span>
                <span className="text-sm font-medium text-red-600">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sites surveillés</span>
                <span className="text-sm font-medium text-orange-600">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Superficie totale</span>
                <span className="text-sm font-medium text-gray-900">12.8 ha</span>
              </div>
            </div>
          </div>
        </div>

        {/* Carte */}
        <div className="lg:col-span-3">
          <div className="card p-0 overflow-hidden">
            <div className="h-[600px] bg-gradient-to-br from-green-100 via-blue-100 to-yellow-100 flex items-center justify-center relative">
              {/* Placeholder pour la carte */}
              <div className="text-center z-10">
                <MapPinIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Carte Interactive Leaflet</h3>
                <p className="text-gray-600 mb-4">Région de Bondoukou, Côte d'Ivoire</p>
                <div className="bg-white rounded-lg p-4 shadow-lg max-w-sm mx-auto">
                  <p className="text-sm text-gray-700 mb-2">Fonctionnalités prévues :</p>
                  <ul className="text-xs text-gray-600 space-y-1 text-left">
                    <li>• Vue satellite et carte standard</li>
                    <li>• Marqueurs d'alertes interactifs</li>
                    <li>• Zones protégées</li>
                    <li>• Sites d'orpaillage connus</li>
                    <li>• Contrôles de couches</li>
                  </ul>
                  <p className="text-xs text-red-600 mt-2 font-medium">
                    (Temporairement désactivé pour éviter les erreurs)
                  </p>
                </div>
              </div>

              {/* Éléments décoratifs simulant des marqueurs */}
              <div className="absolute top-20 left-20 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
              <div className="absolute top-32 right-32 w-4 h-4 bg-orange-500 rounded-full animate-pulse"></div>
              <div className="absolute bottom-24 left-1/3 w-4 h-4 bg-yellow-500 rounded-full animate-pulse"></div>
              <div className="absolute top-1/2 right-20 w-6 h-6 bg-green-500 rounded-full opacity-30"></div>
              <div className="absolute bottom-32 right-1/4 w-6 h-6 bg-blue-500 rounded-full opacity-30"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
