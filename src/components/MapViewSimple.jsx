import React, { useState } from 'react';
import {
  ExclamationTriangleIcon,
  EyeIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

const MapViewSimple = ({ user }) => {
  const [showAlerts, setShowAlerts] = useState(true);
  const [showSites, setShowSites] = useState(true);
  const [showZones, setShowZones] = useState(true);

  // Données mockées pour les alertes et sites
  const alertes = [
    {
      id: 1,
      position: "Bondoukou Centre",
      niveau: 'critique',
      description: 'Site clandestin détecté - activité intense',
      date: '2025-05-27',
      superficie: '2.5 ha'
    },
    {
      id: 2,
      position: "Tanda Est",
      niveau: 'moyen',
      description: 'Expansion possible d\'un site existant',
      date: '2025-05-27',
      superficie: '1.2 ha'
    },
    {
      id: 3,
      position: "Bouna Nord",
      niveau: 'faible',
      description: 'Activité suspecte détectée',
      date: '2025-05-26',
      superficie: '0.8 ha'
    }
  ];

  const sitesConnus = [
    {
      id: 1,
      position: "Bouna Nord",
      nom: 'Site Bouna Nord',
      statut: 'surveillé',
      superficie: '5.2 ha'
    },
    {
      id: 2,
      position: "Tanda Est",
      nom: 'Site Tanda Est',
      statut: 'fermé',
      superficie: '3.1 ha'
    }
  ];

  const zonesProtegees = [
    {
      id: 1,
      nom: 'Zone protégée Bondoukou',
      type: 'Réserve forestière',
      superficie: '15.7 km²'
    },
    {
      id: 2,
      nom: 'Zone protégée Tanda',
      type: 'Parc national',
      superficie: '8.3 km²'
    }
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Carte interactive</h1>
          <p className="text-gray-600 mt-1">
            Surveillance satellite de la région {user.region_autorisee.includes('ALL') ? 'nationale' : user.region_autorisee.join(', ')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <MagnifyingGlassIcon className="h-4 w-4" />
            <span>Rechercher</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <AdjustmentsHorizontalIcon className="h-4 w-4" />
            <span>Filtres</span>
          </button>
        </div>
      </div>

      {/* Contrôles de la carte */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle */}
        <div className="lg:col-span-1 space-y-4">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contrôles</h3>
            
            {/* Couches */}
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Couches</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showAlerts}
                      onChange={(e) => setShowAlerts(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Alertes</span>
                    <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                      {alertes.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showSites}
                      onChange={(e) => setShowSites(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Sites connus</span>
                    <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                      {sitesConnus.length}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showZones}
                      onChange={(e) => setShowZones(e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Zones protégées</span>
                    <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      {zonesProtegees.length}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Légende */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Légende</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Alerte critique</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Site connu</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded-full opacity-30"></div>
                <span className="text-sm text-gray-700">Zone protégée</span>
              </div>
            </div>
          </div>

          {/* Statistiques rapides */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Alertes actives</span>
                <span className="text-sm font-medium text-red-600">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sites surveillés</span>
                <span className="text-sm font-medium text-orange-600">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Superficie totale</span>
                <span className="text-sm font-medium text-gray-900">12.8 ha</span>
              </div>
            </div>
          </div>
        </div>

        {/* Zone de carte */}
        <div className="lg:col-span-3">
          <div className="card p-0 overflow-hidden">
            <div className="h-[600px] bg-gradient-to-br from-green-100 via-blue-100 to-yellow-100 flex items-center justify-center relative">
              {/* Placeholder pour la carte */}
              <div className="text-center z-10">
                <GlobeAltIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Carte Interactive</h3>
                <p className="text-gray-600 mb-4">Région de Bondoukou, Côte d'Ivoire</p>
                <div className="bg-white rounded-lg p-4 shadow-lg max-w-sm mx-auto">
                  <p className="text-sm text-gray-700 mb-2">Fonctionnalités disponibles :</p>
                  <ul className="text-xs text-gray-600 space-y-1 text-left">
                    <li>• Vue des alertes par région</li>
                    <li>• Sites d'orpaillage répertoriés</li>
                    <li>• Zones protégées</li>
                    <li>• Filtres par type et statut</li>
                    <li>• Statistiques en temps réel</li>
                  </ul>
                  <p className="text-xs text-blue-600 mt-2 font-medium">
                    Version simplifiée - Leaflet sera intégré ultérieurement
                  </p>
                </div>
              </div>
              
              {/* Éléments décoratifs simulant des marqueurs */}
              {showAlerts && (
                <>
                  <div className="absolute top-20 left-20 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                  <div className="absolute top-32 right-32 w-4 h-4 bg-yellow-500 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-24 left-1/3 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                </>
              )}
              
              {showSites && (
                <>
                  <div className="absolute top-1/2 right-20 w-4 h-4 bg-orange-500 rounded-full"></div>
                  <div className="absolute bottom-32 right-1/4 w-4 h-4 bg-orange-600 rounded-full"></div>
                </>
              )}
              
              {showZones && (
                <>
                  <div className="absolute top-1/3 left-1/4 w-12 h-12 bg-green-500 rounded-full opacity-20"></div>
                  <div className="absolute bottom-1/3 right-1/3 w-16 h-16 bg-blue-500 rounded-full opacity-20"></div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Liste des éléments visibles */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alertes */}
        {showAlerts && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
              Alertes actives
            </h3>
            <div className="space-y-3">
              {alertes.map((alerte) => (
                <div key={alerte.id} className="p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      alerte.niveau === 'critique' ? 'bg-red-200 text-red-800' :
                      alerte.niveau === 'moyen' ? 'bg-yellow-200 text-yellow-800' :
                      'bg-green-200 text-green-800'
                    }`}>
                      {alerte.niveau.toUpperCase()}
                    </span>
                    <span className="text-xs text-gray-500">{alerte.date}</span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{alerte.position}</p>
                  <p className="text-xs text-gray-600 mt-1">{alerte.description}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {alerte.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sites connus */}
        {showSites && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPinIcon className="h-5 w-5 text-orange-500 mr-2" />
              Sites connus
            </h3>
            <div className="space-y-3">
              {sitesConnus.map((site) => (
                <div key={site.id} className="p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      site.statut === 'surveillé' ? 'bg-orange-200 text-orange-800' : 'bg-gray-200 text-gray-800'
                    }`}>
                      {site.statut.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{site.nom}</p>
                  <p className="text-xs text-gray-600 mt-1">{site.position}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {site.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Zones protégées */}
        {showZones && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <EyeIcon className="h-5 w-5 text-green-500 mr-2" />
              Zones protégées
            </h3>
            <div className="space-y-3">
              {zonesProtegees.map((zone) => (
                <div key={zone.id} className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-900">{zone.nom}</p>
                  <p className="text-xs text-gray-600 mt-1">{zone.type}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {zone.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapViewSimple;
