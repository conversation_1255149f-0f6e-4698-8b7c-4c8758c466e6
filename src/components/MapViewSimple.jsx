import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, Circle, LayersControl } from 'react-leaflet';
import L from 'leaflet';
import {
  ExclamationTriangleIcon,
  EyeIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  SparklesIcon,
  FireIcon
} from '@heroicons/react/24/outline';

// Configuration des icônes Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Icônes personnalisées avec thème ivoirien
const createCustomIcon = (color, size = [25, 41]) => {
  return new L.DivIcon({
    className: 'custom-marker',
    html: `<div style="
      background: ${color};
      width: ${size[0]}px;
      height: ${size[1]}px;
      border-radius: 50% 50% 50% 0;
      border: 3px solid white;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
      transform: rotate(-45deg);
      position: relative;
    ">
      <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(45deg);
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
      "></div>
    </div>`,
    iconSize: size,
    iconAnchor: [size[0]/2, size[1]],
    popupAnchor: [0, -size[1]]
  });
};

const alertIcon = createCustomIcon('#FF8C00'); // Orange CI
const siteIcon = createCustomIcon('#22C55E'); // Vert CI
const criticalIcon = createCustomIcon('#DC2626', [30, 46]); // Rouge plus grand

const MapViewSimple = ({ user }) => {
  const [showAlerts, setShowAlerts] = useState(true);
  const [showSites, setShowSites] = useState(true);
  const [showZones, setShowZones] = useState(true);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Centre de la Côte d'Ivoire (région de Bondoukou)
  const coteIvoireCenter = [8.0402, -2.8000];

  // Données réelles avec coordonnées GPS de la Côte d'Ivoire
  const alertes = [
    {
      id: 1,
      position: [8.0402, -2.8000], // Bondoukou
      lieu: "Bondoukou Centre",
      niveau: 'critique',
      description: 'Site d\'orpaillage clandestin détecté - activité intense',
      date: '2025-05-27',
      superficie: '2.5 ha',
      confidence: 0.94,
      images: ['satellite_1.jpg', 'satellite_2.jpg']
    },
    {
      id: 2,
      position: [8.1333, -3.1667], // Tanda
      lieu: "Tanda Est",
      niveau: 'moyen',
      description: 'Expansion possible d\'un site existant',
      date: '2025-05-27',
      superficie: '1.2 ha',
      confidence: 0.78,
      images: ['satellite_3.jpg']
    },
    {
      id: 3,
      position: [9.2667, -2.9833], // Bouna
      lieu: "Bouna Nord",
      niveau: 'faible',
      description: 'Activité suspecte détectée par IA',
      date: '2025-05-26',
      superficie: '0.8 ha',
      confidence: 0.65,
      images: []
    },
    {
      id: 4,
      position: [7.9167, -2.6167], // Nassian
      lieu: "Nassian Sud",
      niveau: 'critique',
      description: 'Nouveau site détecté près de la rivière',
      date: '2025-05-28',
      superficie: '3.1 ha',
      confidence: 0.91,
      images: ['satellite_4.jpg', 'satellite_5.jpg']
    }
  ];

  const sitesConnus = [
    {
      id: 1,
      position: [9.2667, -2.9833], // Bouna
      lieu: "Bouna Nord",
      nom: 'Site Bouna Nord',
      statut: 'surveillé',
      superficie: '5.2 ha',
      dateCreation: '2024-03-15',
      operateur: 'Coopérative Bouna'
    },
    {
      id: 2,
      position: [8.1333, -3.1667], // Tanda
      lieu: "Tanda Est",
      nom: 'Site Tanda Est',
      statut: 'fermé',
      superficie: '3.1 ha',
      dateCreation: '2023-11-20',
      operateur: 'Société Minière Tanda'
    },
    {
      id: 3,
      position: [8.3500, -2.7000], // Transua
      lieu: "Transua",
      nom: 'Site Transua',
      statut: 'autorisé',
      superficie: '4.8 ha',
      dateCreation: '2024-01-10',
      operateur: 'Mines d\'Or Transua'
    }
  ];

  const zonesProtegees = [
    {
      id: 1,
      center: [8.2000, -2.9000],
      radius: 5000, // 5km
      nom: 'Réserve Forestière de Bondoukou',
      type: 'Réserve forestière',
      superficie: '78.5 km²',
      protection: 'Totale'
    },
    {
      id: 2,
      center: [9.1000, -3.0000],
      radius: 8000, // 8km
      nom: 'Parc National de Bouna',
      type: 'Parc national',
      superficie: '201.3 km²',
      protection: 'Intégrale'
    },
    {
      id: 3,
      center: [7.8000, -2.5000],
      radius: 3000, // 3km
      nom: 'Zone Tampon Nassian',
      type: 'Zone tampon',
      superficie: '28.3 km²',
      protection: 'Partielle'
    }
  ];

  useEffect(() => {
    // Simulation du chargement de la carte
    const timer = setTimeout(() => {
      setMapLoaded(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête avec design ivoirien */}
      <div className="card-ci">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gradient-ci mb-2">
              🇨🇮 Surveillance Satellite - Côte d'Ivoire
            </h1>
            <p className="text-gray-700 text-lg">
              Région de surveillance : <span className="font-semibold text-orange-ci-600">
                {user.region_autorisee.includes('ALL') ? 'Territoire National' : user.region_autorisee.join(', ')}
              </span>
            </p>
            <div className="flex items-center space-x-4 mt-3">
              <div className="flex items-center space-x-2">
                <SparklesIcon className="h-5 w-5 text-orange-ci-500" />
                <span className="text-sm text-gray-600">IA Avancée</span>
              </div>
              <div className="flex items-center space-x-2">
                <FireIcon className="h-5 w-5 text-vert-ci-500" />
                <span className="text-sm text-gray-600">Temps Réel</span>
              </div>
              <div className="flex items-center space-x-2">
                <GlobeAltIcon className="h-5 w-5 text-orange-ci-500" />
                <span className="text-sm text-gray-600">Vue Satellite</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button className="btn-outline flex items-center space-x-2">
              <MagnifyingGlassIcon className="h-5 w-5" />
              <span>Rechercher</span>
            </button>
            <button className="btn-secondary flex items-center space-x-2">
              <AdjustmentsHorizontalIcon className="h-5 w-5" />
              <span>Filtres</span>
            </button>
          </div>
        </div>
      </div>

      {/* Contrôles de la carte */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panneau de contrôle avec design ivoirien */}
        <div className="lg:col-span-1 space-y-4">
          <div className="card-ci">
            <h3 className="text-lg font-semibold text-gradient-ci mb-4 flex items-center">
              <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
              Contrôles de Couches
            </h3>

            {/* Couches avec badges ivoiriens */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Éléments à afficher</label>
                <div className="space-y-3">
                  <label className="flex items-center p-3 rounded-xl border-2 border-orange-ci-200 hover:border-orange-ci-400 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={showAlerts}
                      onChange={(e) => setShowAlerts(e.target.checked)}
                      className="rounded border-orange-ci-300 text-orange-ci-600 focus:ring-orange-ci-500"
                    />
                    <ExclamationTriangleIcon className="h-5 w-5 text-orange-ci-500 ml-2 mr-2" />
                    <span className="text-sm font-medium text-gray-700 flex-1">Alertes Détectées</span>
                    <span className="badge-ci-orange">
                      {alertes.length}
                    </span>
                  </label>

                  <label className="flex items-center p-3 rounded-xl border-2 border-vert-ci-200 hover:border-vert-ci-400 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={showSites}
                      onChange={(e) => setShowSites(e.target.checked)}
                      className="rounded border-vert-ci-300 text-vert-ci-600 focus:ring-vert-ci-500"
                    />
                    <MapPinIcon className="h-5 w-5 text-vert-ci-500 ml-2 mr-2" />
                    <span className="text-sm font-medium text-gray-700 flex-1">Sites Autorisés</span>
                    <span className="badge-ci-vert">
                      {sitesConnus.length}
                    </span>
                  </label>

                  <label className="flex items-center p-3 rounded-xl border-2 border-yellow-200 hover:border-yellow-400 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={showZones}
                      onChange={(e) => setShowZones(e.target.checked)}
                      className="rounded border-yellow-300 text-yellow-600 focus:ring-yellow-500"
                    />
                    <EyeIcon className="h-5 w-5 text-yellow-600 ml-2 mr-2" />
                    <span className="text-sm font-medium text-gray-700 flex-1">Zones Protégées</span>
                    <span className="badge-ci-gold">
                      {zonesProtegees.length}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Légende */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Légende</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Alerte critique</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Site connu</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded-full opacity-30"></div>
                <span className="text-sm text-gray-700">Zone protégée</span>
              </div>
            </div>
          </div>

          {/* Statistiques rapides */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Alertes actives</span>
                <span className="text-sm font-medium text-red-600">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sites surveillés</span>
                <span className="text-sm font-medium text-orange-600">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Superficie totale</span>
                <span className="text-sm font-medium text-gray-900">12.8 ha</span>
              </div>
            </div>
          </div>
        </div>

        {/* Zone de carte avec Leaflet */}
        <div className="lg:col-span-3">
          <div className="card p-0 overflow-hidden relative">
            {!mapLoaded && (
              <div className="absolute inset-0 bg-gradient-to-br from-orange-ci-50 via-white to-vert-ci-50 flex items-center justify-center z-10">
                <div className="text-center">
                  <div className="animate-pulse-ci">
                    <GlobeAltIcon className="h-16 w-16 text-gradient-ci mx-auto mb-4" />
                  </div>
                  <h3 className="text-xl font-bold text-gradient-ci mb-2">Chargement de la carte satellite...</h3>
                  <p className="text-gray-600">Côte d'Ivoire - Région de Bondoukou</p>
                  <div className="animate-shimmer-ci mt-4 h-2 bg-gray-200 rounded-full w-48 mx-auto"></div>
                </div>
              </div>
            )}

            <div className="h-[700px] w-full">
              <MapContainer
                center={coteIvoireCenter}
                zoom={9}
                style={{ height: '100%', width: '100%' }}
                className="rounded-2xl"
              >
                <LayersControl position="topright">
                  <LayersControl.BaseLayer checked name="🛰️ Vue Satellite">
                    <TileLayer
                      url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                      attribution='&copy; <a href="https://www.esri.com/">Esri</a> | Côte d&apos;Ivoire'
                    />
                  </LayersControl.BaseLayer>
                  <LayersControl.BaseLayer name="🗺️ Carte Standard">
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Côte d&apos;Ivoire'
                    />
                  </LayersControl.BaseLayer>
                  <LayersControl.BaseLayer name="🌍 Terrain">
                    <TileLayer
                      url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://opentopomap.org/">OpenTopoMap</a> | Côte d&apos;Ivoire'
                    />
                  </LayersControl.BaseLayer>
                </LayersControl>

                {/* Alertes avec icônes personnalisées */}
                {showAlerts && alertes.map((alerte) => (
                  <Marker
                    key={alerte.id}
                    position={alerte.position}
                    icon={alerte.niveau === 'critique' ? criticalIcon : alertIcon}
                  >
                    <Popup className="custom-popup">
                      <div className="p-3 min-w-[250px]">
                        <div className="flex items-center space-x-2 mb-3">
                          <ExclamationTriangleIcon className="h-6 w-6 text-orange-ci-500" />
                          <span className={`font-bold text-lg ${
                            alerte.niveau === 'critique' ? 'text-red-700' :
                            alerte.niveau === 'moyen' ? 'text-orange-700' : 'text-yellow-700'
                          }`}>
                            Alerte {alerte.niveau.toUpperCase()}
                          </span>
                        </div>
                        <h4 className="font-semibold text-gray-900 mb-2">{alerte.lieu}</h4>
                        <p className="text-sm text-gray-700 mb-3">{alerte.description}</p>
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-3">
                          <div><strong>Date:</strong> {alerte.date}</div>
                          <div><strong>Superficie:</strong> {alerte.superficie}</div>
                          <div><strong>Confiance IA:</strong> {Math.round(alerte.confidence * 100)}%</div>
                          <div><strong>Images:</strong> {alerte.images.length}</div>
                        </div>
                        <button className="w-full btn-secondary text-xs py-2">
                          📊 Voir Détails Complets
                        </button>
                      </div>
                    </Popup>
                  </Marker>
                ))}

                {/* Sites autorisés */}
                {showSites && sitesConnus.map((site) => (
                  <Marker key={site.id} position={site.position} icon={siteIcon}>
                    <Popup className="custom-popup">
                      <div className="p-3 min-w-[250px]">
                        <div className="flex items-center space-x-2 mb-3">
                          <MapPinIcon className="h-6 w-6 text-vert-ci-500" />
                          <span className="font-bold text-lg text-vert-ci-700">{site.nom}</span>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div><strong>Lieu:</strong> {site.lieu}</div>
                          <div><strong>Statut:</strong>
                            <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                              site.statut === 'autorisé' ? 'bg-green-100 text-green-800' :
                              site.statut === 'surveillé' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {site.statut.toUpperCase()}
                            </span>
                          </div>
                          <div><strong>Superficie:</strong> {site.superficie}</div>
                          <div><strong>Opérateur:</strong> {site.operateur}</div>
                          <div><strong>Créé le:</strong> {site.dateCreation}</div>
                        </div>
                        <button className="w-full btn-primary text-xs py-2 mt-3">
                          📋 Historique du Site
                        </button>
                      </div>
                    </Popup>
                  </Marker>
                ))}

                {/* Zones protégées */}
                {showZones && zonesProtegees.map((zone) => (
                  <Circle
                    key={zone.id}
                    center={zone.center}
                    radius={zone.radius}
                    pathOptions={{
                      color: '#FFD700',
                      fillColor: '#FFD700',
                      fillOpacity: 0.15,
                      weight: 3,
                      dashArray: '10, 10'
                    }}
                  >
                    <Popup className="custom-popup">
                      <div className="p-3 min-w-[200px]">
                        <div className="flex items-center space-x-2 mb-3">
                          <EyeIcon className="h-6 w-6 text-yellow-600" />
                          <span className="font-bold text-lg text-yellow-700">{zone.nom}</span>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div><strong>Type:</strong> {zone.type}</div>
                          <div><strong>Superficie:</strong> {zone.superficie}</div>
                          <div><strong>Protection:</strong> {zone.protection}</div>
                        </div>
                        <button className="w-full btn-outline text-xs py-2 mt-3">
                          🛡️ Réglementation
                        </button>
                      </div>
                    </Popup>
                  </Circle>
                ))}
              </MapContainer>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des éléments visibles */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alertes */}
        {showAlerts && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
              Alertes actives
            </h3>
            <div className="space-y-3">
              {alertes.map((alerte) => (
                <div key={alerte.id} className="p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      alerte.niveau === 'critique' ? 'bg-red-200 text-red-800' :
                      alerte.niveau === 'moyen' ? 'bg-yellow-200 text-yellow-800' :
                      'bg-green-200 text-green-800'
                    }`}>
                      {alerte.niveau.toUpperCase()}
                    </span>
                    <span className="text-xs text-gray-500">{alerte.date}</span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{alerte.position}</p>
                  <p className="text-xs text-gray-600 mt-1">{alerte.description}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {alerte.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sites connus */}
        {showSites && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPinIcon className="h-5 w-5 text-orange-500 mr-2" />
              Sites connus
            </h3>
            <div className="space-y-3">
              {sitesConnus.map((site) => (
                <div key={site.id} className="p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                      site.statut === 'surveillé' ? 'bg-orange-200 text-orange-800' : 'bg-gray-200 text-gray-800'
                    }`}>
                      {site.statut.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{site.nom}</p>
                  <p className="text-xs text-gray-600 mt-1">{site.position}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {site.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Zones protégées */}
        {showZones && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <EyeIcon className="h-5 w-5 text-green-500 mr-2" />
              Zones protégées
            </h3>
            <div className="space-y-3">
              {zonesProtegees.map((zone) => (
                <div key={zone.id} className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-900">{zone.nom}</p>
                  <p className="text-xs text-gray-600 mt-1">{zone.type}</p>
                  <p className="text-xs text-gray-500 mt-1">Superficie: {zone.superficie}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapViewSimple;
