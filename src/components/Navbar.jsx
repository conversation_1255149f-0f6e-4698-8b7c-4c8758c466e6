import React, { useState, useEffect } from 'react';
import {
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Avatar from './Avatar';

const Navbar = ({ user, onLogout, onToggleSidebar }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Mise à jour de l'heure en temps réel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b-2 border-gradient-to-r from-orange-ci-200 to-vert-ci-200 h-16 flex items-center justify-between px-6 relative z-30">
      {/* Partie gauche avec design ivoirien */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onToggleSidebar}
          className="p-2 rounded-xl hover:bg-gradient-to-r hover:from-orange-ci-100 hover:to-vert-ci-100 transition-all duration-300 lg:hidden transform hover:scale-105"
        >
          <Bars3Icon className="h-5 w-5 text-orange-ci-600" />
        </button>

        <div className="flex items-center space-x-3 text-sm">
          <div className="p-2 rounded-lg bg-gradient-to-r from-orange-ci-100 to-vert-ci-100">
            <ClockIcon className="h-4 w-4 text-orange-ci-600" />
          </div>
          <div>
            <span className="font-bold text-gradient-ci">{formatTime(currentTime)}</span>
            <span className="ml-3 text-gray-600 font-medium">{formatDate(currentTime)}</span>
          </div>
        </div>
      </div>

      {/* Partie droite avec design ivoirien */}
      <div className="flex items-center space-x-4">
        {/* Indicateur de région avec badge ivoirien */}
        <div className="hidden md:flex items-center space-x-3">
          <span className="text-sm text-orange-ci-600 font-medium">🌍 Région:</span>
          <span className="px-3 py-1 text-sm font-bold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 rounded-full border border-blue-300">
            {user.region_autorisee.includes('ALL') ? '🇨🇮 Nationale' : user.region_autorisee.join(', ')}
          </span>
        </div>

        {/* Notifications avec design ivoirien */}
        <div className="relative">
          <button className="relative p-2 rounded-xl hover:bg-gradient-to-r hover:from-orange-ci-100 hover:to-vert-ci-100 transition-all duration-300 transform hover:scale-105">
            <BellIcon className="h-5 w-5 text-orange-ci-600" />
            <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg animate-pulse-ci">
              3
            </span>
          </button>
        </div>

        {/* Menu utilisateur avec design ivoirien */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gradient-to-r hover:from-orange-ci-100 hover:to-vert-ci-100 transition-all duration-300 transform hover:scale-105"
          >
            <Avatar user={user} size="md" />
            <span className="hidden md:block text-sm font-semibold text-gray-700">
              {user.prenoms} {user.nom}
            </span>
          </button>

          {/* Menu déroulant utilisateur avec design ivoirien */}
          {showUserMenu && (
            <>
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-2xl shadow-2xl border-2 border-orange-ci-200 z-50 overflow-hidden">
                <div className="p-4 bg-gradient-to-r from-orange-ci-50 to-vert-ci-50 border-b border-orange-ci-200">
                  <div className="flex items-center space-x-3">
                    <Avatar user={user} size="md" />
                    <div>
                      <p className="text-sm font-bold text-gray-900">
                        {user.prenoms} {user.nom}
                      </p>
                      <p className="text-xs text-orange-ci-600 font-medium">{user.email}</p>
                      <p className="text-xs text-gray-600">{user.fonction}</p>
                    </div>
                  </div>
                </div>
                <div className="py-2">
                  <button
                    onClick={onLogout}
                    className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 hover:text-red-700 transition-all duration-300"
                  >
                    <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
                    🚪 Se déconnecter
                  </button>
                </div>
              </div>
              {/* Overlay corrigé - ne bloque plus les interactions */}
              <div
                className="fixed inset-0 z-40 bg-transparent"
                onClick={() => setShowUserMenu(false)}
              ></div>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
