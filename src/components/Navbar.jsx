import React, { useState, useEffect } from 'react';
import {
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { mockAlertes } from '../data/mocks';

const Navbar = ({ user, onLogout, onToggleSidebar }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Mise à jour de l'heure en temps réel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulation des notifications en temps réel
  useEffect(() => {
    // Filtrer les alertes selon les permissions de l'utilisateur
    const userAlerts = mockAlertes.filter(alerte => {
      if (user.niveau_acces === 'NATIONAL') return true;
      if (user.niveau_acces === 'REGIONAL') {
        return user.region_autorisee.includes(alerte.region);
      }
      return user.region_autorisee.includes(alerte.region) && alerte.niveau === 'critique';
    });

    setNotifications(userAlerts.slice(0, 5)); // Limiter à 5 notifications
  }, [user]);

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getNotificationColor = (niveau) => {
    switch (niveau) {
      case 'critique': return 'text-red-600 bg-red-50';
      case 'moyen': return 'text-yellow-600 bg-yellow-50';
      case 'faible': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const unreadNotifications = notifications.filter(n => n.statut === 'nouveau').length;

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 h-16 flex items-center justify-between px-6">
      {/* Partie gauche */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onToggleSidebar}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 lg:hidden"
        >
          <Bars3Icon className="h-5 w-5 text-gray-500" />
        </button>

        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <ClockIcon className="h-4 w-4" />
          <div>
            <span className="font-medium">{formatTime(currentTime)}</span>
            <span className="ml-2 text-gray-500">{formatDate(currentTime)}</span>
          </div>
        </div>
      </div>

      {/* Partie droite */}
      <div className="flex items-center space-x-4">
        {/* Indicateur de région */}
        <div className="hidden md:flex items-center space-x-2">
          <span className="text-sm text-gray-500">Région:</span>
          <span className="text-sm font-medium text-gray-900">
            {user.region_autorisee.includes('ALL') ? 'Nationale' : user.region_autorisee.join(', ')}
          </span>
        </div>

        {/* Notifications */}
        <div className="relative">
          <button
            onClick={() => setShowNotifications(!showNotifications)}
            className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <BellIcon className="h-5 w-5 text-gray-500" />
            {unreadNotifications > 0 && (
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {unreadNotifications}
              </span>
            )}
          </button>

          {/* Menu des notifications */}
          {showNotifications && (
            <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-sm font-medium text-gray-900">
                  Notifications ({notifications.length})
                </h3>
              </div>
              <div className="max-h-64 overflow-y-auto">
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.statut === 'nouveau' ? 'bg-red-500' : 'bg-gray-300'
                        }`}></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {notification.description}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {notification.region} • {new Date(notification.date).toLocaleString('fr-FR')}
                          </p>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${
                            getNotificationColor(notification.niveau)
                          }`}>
                            {notification.niveau}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500 text-sm">
                    Aucune notification
                  </div>
                )}
              </div>
              <div className="p-3 border-t border-gray-200">
                <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                  Voir toutes les notifications
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Menu utilisateur */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <UserCircleIcon className="h-6 w-6 text-gray-500" />
            <span className="hidden md:block text-sm font-medium text-gray-700">
              {user.prenoms} {user.nom}
            </span>
          </button>

          {/* Menu déroulant utilisateur */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              <div className="p-3 border-b border-gray-200">
                <p className="text-sm font-medium text-gray-900">
                  {user.prenoms} {user.nom}
                </p>
                <p className="text-xs text-gray-500">{user.email}</p>
                <p className="text-xs text-gray-500">{user.fonction}</p>
              </div>
              <div className="py-1">
                <button
                  onClick={onLogout}
                  className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                  Se déconnecter
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Overlay pour fermer les menus */}
      {(showNotifications || showUserMenu) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowNotifications(false);
            setShowUserMenu(false);
          }}
        ></div>
      )}
    </header>
  );
};

export default Navbar;
