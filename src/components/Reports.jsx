import React, { useState } from 'react';
import {
  DocumentTextIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
  ChartBarIcon,
  MapIcon,
  ClockIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

const Reports = ({ user }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [reportType, setReportType] = useState('summary');

  const reportTypes = [
    {
      id: 'summary',
      name: 'Rapport de synthèse',
      description: 'Vue d\'ensemble des activités de surveillance',
      icon: ChartBarIcon,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      id: 'alerts',
      name: 'Rapport d\'alertes',
      description: 'Détail des alertes et interventions',
      icon: DocumentTextIcon,
      color: 'bg-red-100 text-red-600'
    },
    {
      id: 'geographic',
      name: 'Rapport géographique',
      description: 'Analyse par région et département',
      icon: MapIcon,
      color: 'bg-green-100 text-green-600'
    },
    {
      id: 'temporal',
      name: 'Analy<PERSON> temporelle',
      description: 'Évolution des détections dans le temps',
      icon: ClockIcon,
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  const predefinedReports = [
    {
      id: 1,
      name: 'Rapport mensuel Mai 2025',
      type: 'Synthèse',
      date: '2025-05-27',
      region: 'Zanzan',
      status: 'Généré',
      size: '2.4 MB'
    },
    {
      id: 2,
      name: 'Alertes critiques - Semaine 21',
      type: 'Alertes',
      date: '2025-05-20',
      region: 'Toutes',
      status: 'Généré',
      size: '1.8 MB'
    },
    {
      id: 3,
      name: 'Analyse géographique Q2',
      type: 'Géographique',
      date: '2025-05-15',
      region: 'Zanzan',
      status: 'En cours',
      size: '-'
    }
  ];

  const handleGenerateReport = () => {
    alert(`Génération du rapport ${reportTypes.find(t => t.id === reportType)?.name} pour la période ${selectedPeriod} en cours...`);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Rapports</h1>
          <p className="text-gray-600 mt-1">
            Génération et export de rapports de surveillance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn-outline flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4" />
            <span>Filtres avancés</span>
          </button>
          <button
            onClick={handleGenerateReport}
            className="btn-primary flex items-center space-x-2"
          >
            <DocumentTextIcon className="h-4 w-4" />
            <span>Nouveau rapport</span>
          </button>
        </div>
      </div>

      {/* Configuration du rapport */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Types de rapports */}
        <div className="lg:col-span-2">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Types de rapports</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {reportTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <div
                    key={type.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      reportType === type.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setReportType(type.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${type.color}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{type.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Paramètres */}
        <div className="space-y-4">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Paramètres</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Période
                </label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                  <option value="quarter">Ce trimestre</option>
                  <option value="year">Cette année</option>
                  <option value="custom">Période personnalisée</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Région
                </label>
                <select
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Toutes les régions</option>
                  <option value="zanzan">Zanzan</option>
                  <option value="denguele">Denguélé</option>
                  <option value="bounkani">Bounkani</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Format d'export
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="radio" name="format" value="pdf" defaultChecked className="text-primary-600" />
                    <span className="ml-2 text-sm text-gray-700">PDF</span>
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="format" value="excel" className="text-primary-600" />
                    <span className="ml-2 text-sm text-gray-700">Excel</span>
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="format" value="csv" className="text-primary-600" />
                    <span className="ml-2 text-sm text-gray-700">CSV</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Aperçu</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{reportTypes.find(t => t.id === reportType)?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Période:</span>
                <span className="font-medium">{selectedPeriod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Région:</span>
                <span className="font-medium">{selectedRegion === 'all' ? 'Toutes' : selectedRegion}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Rapports existants */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Rapports récents</h3>
          <button className="text-sm text-primary-600 hover:text-primary-700">
            Voir tous
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nom du rapport
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Région
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {predefinedReports.map((report) => (
                <tr key={report.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{report.name}</div>
                        <div className="text-sm text-gray-500">{report.size}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {report.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(report.date).toLocaleDateString('fr-FR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {report.region}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      report.status === 'Généré'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {report.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {report.status === 'Généré' && (
                      <button className="text-primary-600 hover:text-primary-900 flex items-center space-x-1">
                        <ArrowDownTrayIcon className="h-4 w-4" />
                        <span>Télécharger</span>
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Reports;
