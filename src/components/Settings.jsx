import React, { useState } from 'react';
import {
  CogIcon,
  ShieldCheckIcon,
  BellIcon,
  UserGroupIcon,
  ComputerDesktopIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { mockIASettings } from '../data/mocks';

const Settings = ({ user }) => {
  const [activeTab, setActiveTab] = useState('ia');
  const [iaSettings, setIaSettings] = useState(mockIASettings);
  const [showSaveNotification, setShowSaveNotification] = useState(false);

  const tabs = [
    { id: 'ia', name: 'Intelligence Artificielle', icon: ComputerDesktopIcon },
    { id: 'alerts', name: 'Alert<PERSON>', icon: BellIcon },
    { id: 'users', name: 'Utilisateurs', icon: UserGroupIcon },
    { id: 'system', name: 'Syst<PERSON>', icon: CogIcon },
    { id: 'security', name: '<PERSON>é<PERSON>rit<PERSON>', icon: ShieldCheckIcon }
  ];

  const handleSaveSettings = () => {
    setShowSaveNotification(true);
    setTimeout(() => setShowSaveNotification(false), 3000);
  };

  const handleIASettingChange = (key, value) => {
    setIaSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Paramètres</h1>
          <p className="text-gray-600 mt-1">
            Configuration système et paramètres IA - Accès administrateur
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSaveSettings}
            className="btn-primary flex items-center space-x-2"
          >
            <CheckCircleIcon className="h-4 w-4" />
            <span>Sauvegarder</span>
          </button>
        </div>
      </div>

      {/* Notification de sauvegarde */}
      {showSaveNotification && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-slide-up">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="h-5 w-5" />
            <span>Paramètres sauvegardés avec succès</span>
          </div>
        </div>
      )}

      {/* Vérification des permissions */}
      {user.niveau_acces !== 'NATIONAL' && (
        <div className="card">
          <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-red-800">Accès restreint</h3>
              <p className="text-red-600">
                Cette section est réservée aux administrateurs nationaux.
                Votre niveau d'accès actuel : <strong>{user.niveau_acces}</strong>
              </p>
            </div>
          </div>
        </div>
      )}

      {user.niveau_acces === 'NATIONAL' && (
        <>
          {/* Onglets */}
          <div className="card p-0">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-primary-500 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            <div className="p-6">
              {/* Onglet IA */}
              {activeTab === 'ia' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Paramètres de l'Intelligence Artificielle
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Configuration des seuils de détection et des algorithmes d'analyse satellite
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Seuil de détection général
                        </label>
                        <input
                          type="range"
                          min="0.5"
                          max="1"
                          step="0.05"
                          value={iaSettings.seuil_detection}
                          onChange={(e) => handleIASettingChange('seuil_detection', parseFloat(e.target.value))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>0.5 (Sensible)</span>
                          <span className="font-medium">{Math.round(iaSettings.seuil_detection * 100)}%</span>
                          <span>1.0 (Strict)</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Seuil alerte critique
                        </label>
                        <input
                          type="range"
                          min="0.7"
                          max="1"
                          step="0.05"
                          value={iaSettings.seuil_alerte_critique}
                          onChange={(e) => handleIASettingChange('seuil_alerte_critique', parseFloat(e.target.value))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>0.7</span>
                          <span className="font-medium">{Math.round(iaSettings.seuil_alerte_critique * 100)}%</span>
                          <span>1.0</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Seuil alerte moyenne
                        </label>
                        <input
                          type="range"
                          min="0.5"
                          max="0.9"
                          step="0.05"
                          value={iaSettings.seuil_alerte_moyenne}
                          onChange={(e) => handleIASettingChange('seuil_alerte_moyenne', parseFloat(e.target.value))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>0.5</span>
                          <span className="font-medium">{Math.round(iaSettings.seuil_alerte_moyenne * 100)}%</span>
                          <span>0.9</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Fréquence d'analyse
                        </label>
                        <select
                          value={iaSettings.frequence_analyse}
                          onChange={(e) => handleIASettingChange('frequence_analyse', e.target.value)}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        >
                          <option value="quotidienne">Quotidienne</option>
                          <option value="bi-quotidienne">Bi-quotidienne</option>
                          <option value="hebdomadaire">Hebdomadaire</option>
                          <option value="temps-reel">Temps réel</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Types de changements détectés
                        </label>
                        <div className="space-y-2">
                          {iaSettings.types_changements.map((type, index) => (
                            <label key={index} className="flex items-center">
                              <input
                                type="checkbox"
                                defaultChecked
                                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                              />
                              <span className="ml-2 text-sm text-gray-700 capitalize">
                                {type.replace('_', ' ')}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">Statistiques actuelles</h4>
                        <div className="space-y-1 text-sm text-blue-800">
                          <div className="flex justify-between">
                            <span>Détections ce mois:</span>
                            <span className="font-medium">28</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Précision moyenne:</span>
                            <span className="font-medium">87%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Faux positifs:</span>
                            <span className="font-medium">3%</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h4 className="font-medium text-yellow-900 mb-2">Dernière mise à jour</h4>
                        <p className="text-sm text-yellow-800">
                          {new Date(iaSettings.derniere_mise_a_jour).toLocaleString('fr-FR')}
                        </p>
                        <button className="mt-2 text-sm text-yellow-700 hover:text-yellow-900 font-medium">
                          Forcer une nouvelle analyse
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Onglet Alertes */}
              {activeTab === 'alerts' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Configuration des Alertes
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Paramètres de notification et de gestion des alertes
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                          <span className="ml-2 text-sm text-gray-700">Notifications email automatiques</span>
                        </label>
                      </div>
                      <div>
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                          <span className="ml-2 text-sm text-gray-700">Notifications SMS pour alertes critiques</span>
                        </label>
                      </div>
                      <div>
                        <label className="flex items-center">
                          <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                          <span className="ml-2 text-sm text-gray-700">Escalade automatique après 2h</span>
                        </label>
                      </div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">
                        Configuration des notifications et escalades pour les différents types d'alertes.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Autres onglets avec contenu placeholder */}
              {activeTab === 'users' && (
                <div className="text-center py-12">
                  <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Gestion des utilisateurs</h3>
                  <p className="text-gray-600">Interface de gestion des utilisateurs en développement</p>
                </div>
              )}

              {activeTab === 'system' && (
                <div className="text-center py-12">
                  <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Paramètres système</h3>
                  <p className="text-gray-600">Configuration système en développement</p>
                </div>
              )}

              {activeTab === 'security' && (
                <div className="text-center py-12">
                  <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Paramètres de sécurité</h3>
                  <p className="text-gray-600">Configuration de sécurité en développement</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Settings;
