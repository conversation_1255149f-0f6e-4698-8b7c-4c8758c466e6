import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  MapIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  ClockIcon,
  CogIcon,
  ShieldCheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ user, isOpen, onToggle }) => {
  const navigationItems = [
    {
      name: 'Tableau de bord',
      href: '/dashboard',
      icon: HomeIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Carte interactive',
      href: '/map',
      icon: MapIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: '<PERSON>ert<PERSON>',
      href: '/alerts',
      icon: ExclamationTriangleIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Rapports',
      href: '/reports',
      icon: DocumentTextIcon,
      access: ['NAT<PERSON><PERSON>', 'REG<PERSON><PERSON>', 'LOCAL']
    },
    {
      name: 'Historique',
      href: '/history',
      icon: ClockIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Paramètres',
      href: '/settings',
      icon: CogIcon,
      access: ['NATIONAL'] // Seuls les administrateurs nationaux
    }
  ];

  const filteredNavigation = navigationItems.filter(item =>
    item.access.includes(user.niveau_acces)
  );

  return (
    <div className={`fixed inset-y-0 left-0 z-50 bg-white shadow-xl border-r border-gray-200 transition-all duration-300 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* En-tête de la sidebar */}
      <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        {isOpen && (
          <div className="flex items-center space-x-2">
            <ShieldCheckIcon className="h-8 w-8 text-primary-500" />
            <div>
              <h1 className="text-sm font-bold text-gray-900">Surveillance</h1>
              <p className="text-xs text-gray-500">Orpaillage</p>
            </div>
          </div>
        )}

        <button
          onClick={onToggle}
          className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200"
        >
          {isOpen ? (
            <ChevronLeftIcon className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronRightIcon className="h-5 w-5 text-gray-500" />
          )}
        </button>
      </div>

      {/* Informations utilisateur */}
      <div className="p-4 border-b border-gray-200">
        {isOpen ? (
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
              <span className="text-primary-600 font-medium text-sm">
                {user.prenoms.charAt(0)}{user.nom.charAt(0)}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.prenoms} {user.nom}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.fonction}
              </p>
              <div className="flex items-center mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  user.niveau_acces === 'NATIONAL'
                    ? 'bg-red-100 text-red-800'
                    : user.niveau_acces === 'REGIONAL'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {user.niveau_acces}
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
              <span className="text-primary-600 font-medium text-sm">
                {user.prenoms.charAt(0)}{user.nom.charAt(0)}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {filteredNavigation.map((item) => {
          const Icon = item.icon;
          return (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `nav-link group ${isActive ? 'active' : ''} ${!isOpen ? 'justify-center' : ''}`
              }
            >
              <Icon className={`h-5 w-5 ${isOpen ? 'mr-3' : ''} flex-shrink-0`} />
              {isOpen && (
                <span className="truncate">{item.name}</span>
              )}
              {!isOpen && (
                <div className="absolute left-16 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                  {item.name}
                </div>
              )}
            </NavLink>
          );
        })}
      </nav>

      {/* Région autorisée */}
      {isOpen && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-1">Région(s) autorisée(s)</div>
          <div className="flex flex-wrap gap-1">
            {user.region_autorisee.map((region, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
              >
                {region === 'ALL' ? 'Toutes les régions' : region}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Indicateur de connexion sécurisée */}
      <div className={`p-4 border-t border-gray-200 ${!isOpen ? 'px-2' : ''}`}>
        {isOpen ? (
          <div className="flex items-center space-x-2 text-xs text-green-600">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Connexion sécurisée</span>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
