import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  MapIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  ClockIcon,
  CogIcon,
  ShieldCheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';

const SidebarFixed = ({ user, isOpen, onToggle }) => {
  const navigationItems = [
    {
      name: 'Tableau de bord',
      href: '/dashboard',
      icon: HomeIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Carte interactive',
      href: '/map',
      icon: MapIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Alert<PERSON>',
      href: '/alerts',
      icon: ExclamationTriangleIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOCAL']
    },
    {
      name: 'Rapports',
      href: '/reports',
      icon: DocumentTextIcon,
      access: ['NAT<PERSON><PERSON>', 'R<PERSON><PERSON><PERSON>', 'LOCAL']
    },
    {
      name: 'Histor<PERSON>',
      href: '/history',
      icon: ClockIcon,
      access: ['NATIONAL', 'REGIONAL', 'LOC<PERSON>']
    },
    {
      name: 'Paramètres',
      href: '/settings',
      icon: CogIcon,
      access: ['NATIONAL']
    }
  ];

  const filteredNavigation = navigationItems.filter(item =>
    item.access.includes(user.niveau_acces)
  );

  return (
    <div className={`fixed inset-y-0 left-0 z-50 transition-all duration-300 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* Arrière-plan solide avec gradient ivoirien */}
      <div className="absolute inset-0 bg-white"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-orange-ci-50/30 via-white to-vert-ci-50/30"></div>
      <div className="absolute inset-y-0 right-0 w-px bg-gradient-to-b from-orange-ci-300 via-vert-ci-300 to-orange-ci-300 shadow-sm"></div>

      {/* Contenu de la sidebar */}
      <div className="relative h-full flex flex-col">
        {/* En-tête avec design ivoirien */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-orange-ci-200/50">
          {isOpen && (
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-orange-ci-500 to-vert-ci-500 shadow-lg">
                <ShieldCheckIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-sm font-bold text-gradient-ci">🇨🇮 Surveillance</h1>
                <p className="text-xs text-orange-ci-600 font-medium">Orpaillage CI</p>
              </div>
            </div>
          )}
          {!isOpen && (
            <div className="flex justify-center w-full">
              <div className="p-2 rounded-xl bg-gradient-to-br from-orange-ci-500 to-vert-ci-500 shadow-lg">
                <ShieldCheckIcon className="h-6 w-6 text-white" />
              </div>
            </div>
          )}

          <button
            onClick={onToggle}
            className="p-2 rounded-xl hover:bg-orange-ci-100 transition-all duration-300 transform hover:scale-105"
          >
            {isOpen ? (
              <ChevronLeftIcon className="h-5 w-5 text-orange-ci-600" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-orange-ci-600" />
            )}
          </button>
        </div>

        {/* Informations utilisateur avec design ivoirien */}
        <div className="p-4 border-b border-orange-ci-200/50">
          {isOpen ? (
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-orange-ci-400 to-vert-ci-400 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">
                  {user.prenoms.charAt(0)}{user.nom.charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {user.prenoms} {user.nom}
                </p>
                <p className="text-xs text-orange-ci-600 truncate font-medium">
                  {user.fonction}
                </p>
                <div className="flex items-center mt-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${
                    user.niveau_acces === 'NATIONAL'
                      ? 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300'
                      : user.niveau_acces === 'REGIONAL'
                      ? 'bg-gradient-to-r from-orange-ci-100 to-orange-ci-200 text-orange-ci-800 border border-orange-ci-300'
                      : 'bg-gradient-to-r from-vert-ci-100 to-vert-ci-200 text-vert-ci-800 border border-vert-ci-300'
                  }`}>
                    {user.niveau_acces}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-orange-ci-400 to-vert-ci-400 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">
                  {user.prenoms.charAt(0)}{user.nom.charAt(0)}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Navigation avec design ivoirien */}
        <nav className="flex-1 px-3 py-6 space-y-2">
          {filteredNavigation.map((item) => {
            const Icon = item.icon;
            return (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `group relative flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-300 transform hover:scale-105 ${
                    isActive
                      ? 'bg-gradient-to-r from-orange-ci-500 to-vert-ci-500 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-orange-ci-100 hover:to-vert-ci-100 hover:text-orange-ci-700'
                  } ${!isOpen ? 'justify-center' : ''}`
                }
              >
                <Icon className={`h-5 w-5 ${isOpen ? 'mr-3' : ''} flex-shrink-0`} />
                {isOpen && (
                  <span className="truncate">{item.name}</span>
                )}
                {!isOpen && (
                  <div className="absolute left-16 bg-gradient-to-r from-orange-ci-600 to-vert-ci-600 text-white text-xs rounded-lg py-2 px-3 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 shadow-lg">
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-orange-ci-600 rotate-45"></div>
                    {item.name}
                  </div>
                )}
              </NavLink>
            );
          })}
        </nav>

        {/* Région autorisée avec design ivoirien */}
        {isOpen && (
          <div className="p-4 border-t border-orange-ci-200/50">
            <div className="text-xs text-orange-ci-600 mb-3 font-semibold">🌍 Région(s) autorisée(s)</div>
            <div className="flex flex-wrap gap-2">
              {user.region_autorisee.map((region, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-semibold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 shadow-sm"
                >
                  {region === 'ALL' ? '🇨🇮 Territoire National' : region}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Indicateur de connexion sécurisée avec design ivoirien */}
        <div className={`p-4 border-t border-orange-ci-200/50 ${!isOpen ? 'px-2' : ''}`}>
          {isOpen ? (
            <div className="flex items-center space-x-3 text-xs">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-gradient-to-r from-vert-ci-500 to-vert-ci-400 rounded-full animate-pulse shadow-sm"></div>
                <span className="text-vert-ci-600 font-semibold">🔒 Connexion Sécurisée</span>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="h-3 w-3 bg-gradient-to-r from-vert-ci-500 to-vert-ci-400 rounded-full animate-pulse shadow-sm"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SidebarFixed;
