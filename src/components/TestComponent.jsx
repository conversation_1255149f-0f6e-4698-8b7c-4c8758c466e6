import React from 'react';

const TestComponent = () => {
  console.log('TestComponent is rendering');

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#3B82F6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '0.5rem',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: '1rem'
        }}>
          🎉 Test Component
        </h1>
        <p style={{ color: '#6B7280' }}>
          Si vous voyez ceci, React fonctionne !
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#DCFCE7',
          borderRadius: '0.25rem'
        }}>
          <p style={{ color: '#166534' }}>
            ✅ JavaScript et React fonctionnent !
          </p>
        </div>
        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#FEF3C7',
          borderRadius: '0.25rem'
        }}>
          <p style={{ color: '#92400E' }}>
            🔧 Styles inline utilisés pour éviter les problèmes CSS
          </p>
        </div>
      </div>
    </div>
  );
};

export default TestComponent;
