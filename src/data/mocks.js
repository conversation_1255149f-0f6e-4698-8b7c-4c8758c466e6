// Données mockées pour la plateforme de surveillance d'orpaillage
// Simulation des API backend pour le développement frontend

// Utilisateurs mockés avec différents niveaux d'accès
export const mockUsers = [
  {
    id: 1,
    email: "<EMAIL>",
    nom: "<PERSON><PERSON><PERSON>",
    prenoms: "<PERSON>",
    fonction: "Directeur Régional",
    region_autorisee: ["Zanzan"],
    niveau_acces: "REGIONAL",
    avatar: "/api/placeholder/40/40",
    derniere_connexion: "2025-05-27T10:30:00Z"
  },
  {
    id: 2,
    email: "<EMAIL>",
    nom: "<PERSON>ra<PERSON><PERSON>",
    prenoms: "Marie Awa",
    fonction: "Administrateur Central",
    region_autorisee: ["ALL"],
    niveau_acces: "NATIONAL",
    avatar: "/api/placeholder/40/40",
    derniere_connexion: "2025-05-27T09:15:00Z"
  },
  {
    id: 3,
    email: "<EMAIL>",
    nom: "Kon<PERSON>",
    prenoms: "<PERSON>",
    fonction: "Agent de Terrain",
    region_autorisee: ["Zanzan"],
    niveau_acces: "LOCAL",
    avatar: "/api/placeholder/40/40",
    derniere_connexion: "2025-05-27T08:45:00Z"
  },
  {
    id: 4,
    email: "<EMAIL>",
    nom: "Diallo",
    prenoms: "Fatou",
    fonction: "Analyste Technique",
    region_autorisee: ["Zanzan", "Denguele"],
    niveau_acces: "REGIONAL",
    avatar: "/api/placeholder/40/40",
    derniere_connexion: "2025-05-27T11:20:00Z"
  }
];

// Régions et sous-régions de Côte d'Ivoire (focus sur Bondoukou)
export const mockRegions = {
  "zanzan": {
    nom: "Zanzan",
    capitale: "Bondoukou",
    departements: ["Bondoukou", "Bouna", "Tanda"],
    coordonnees: { lat: 8.0402, lng: -2.8000 },
    population: 956284,
    superficie: 38251
  },
  "denguele": {
    nom: "Denguélé",
    capitale: "Odienné",
    departements: ["Odienné", "Minignan"],
    coordonnees: { lat: 9.5000, lng: -7.5667 },
    population: 365000,
    superficie: 20997
  },
  "bounkani": {
    nom: "Bounkani",
    capitale: "Bouna",
    departements: ["Bouna", "Doropo", "Nassian"],
    coordonnees: { lat: 9.2667, lng: -2.9833 },
    population: 267167,
    superficie: 17777
  }
};

// Alertes mockées avec différents niveaux de criticité
export const mockAlertes = [
  {
    id: 1,
    region: "Zanzan",
    departement: "Bondoukou",
    niveau: "critique",
    date: "2025-05-27T14:30:00Z",
    coordonnees: { lat: 8.0402, lng: -2.8000 },
    description: "Site clandestin détecté près de Bouna - activité intense",
    confidence: 0.92,
    superficie_estimee: 2.5,
    statut: "nouveau",
    images: ["/api/placeholder/300/200", "/api/placeholder/300/200"],
    agent_responsable: "Ibrahim Koné",
    actions_requises: ["Vérification terrain", "Rapport urgent"]
  },
  {
    id: 2,
    region: "Zanzan",
    departement: "Tanda",
    niveau: "moyen",
    date: "2025-05-27T12:15:00Z",
    coordonnees: { lat: 7.8000, lng: -3.1667 },
    description: "Expansion possible d'un site existant",
    confidence: 0.78,
    superficie_estimee: 1.2,
    statut: "en_cours",
    images: ["/api/placeholder/300/200"],
    agent_responsable: "Jean Baptiste Kouamé",
    actions_requises: ["Surveillance continue"]
  },
  {
    id: 3,
    region: "Zanzan",
    departement: "Bondoukou",
    niveau: "faible",
    date: "2025-05-27T09:45:00Z",
    coordonnees: { lat: 8.1000, lng: -2.7500 },
    description: "Activité suspecte détectée - nécessite confirmation",
    confidence: 0.65,
    superficie_estimee: 0.8,
    statut: "resolu",
    images: ["/api/placeholder/300/200"],
    agent_responsable: "Fatou Diallo",
    actions_requises: ["Archivé"]
  }
];

// Détections d'images satellites mockées
export const mockDetections = [
  {
    id: 1,
    date_acquisition: "2025-05-27T10:00:00Z",
    satellite: "Sentinel-2",
    region: "Zanzan",
    coordonnees: { lat: 8.0402, lng: -2.8000 },
    image_avant: "/api/placeholder/400/300",
    image_apres: "/api/placeholder/400/300",
    changements_detectes: [
      {
        type: "deforestation",
        superficie: 2.1,
        confidence: 0.89
      },
      {
        type: "excavation",
        superficie: 0.4,
        confidence: 0.95
      }
    ],
    statut_validation: "confirme",
    validateur: "Marie Awa Traoré"
  },
  {
    id: 2,
    date_acquisition: "2025-05-26T14:30:00Z",
    satellite: "Sentinel-2",
    region: "Zanzan",
    coordonnees: { lat: 7.9500, lng: -2.9000 },
    image_avant: "/api/placeholder/400/300",
    image_apres: "/api/placeholder/400/300",
    changements_detectes: [
      {
        type: "activite_miniere",
        superficie: 1.8,
        confidence: 0.82
      }
    ],
    statut_validation: "en_attente",
    validateur: null
  }
];

// Statistiques du dashboard mockées
export const mockStats = {
  alertes_actives: 12,
  zones_surveillees: 45,
  detections_mois: 28,
  sites_confirmes: 8,
  superficie_affectee: 15.7, // en hectares
  evolution_mensuelle: {
    janvier: 5,
    fevrier: 8,
    mars: 12,
    avril: 18,
    mai: 28
  },
  repartition_par_region: {
    "Zanzan": 18,
    "Denguélé": 7,
    "Bounkani": 3
  }
};

// Logs d'audit mockés
export const mockAuditLogs = [
  {
    id: 1,
    timestamp: "2025-05-27T14:30:00Z",
    utilisateur: "<EMAIL>",
    action: "CONSULTATION_ALERTE",
    ressource: "Alerte #1",
    ip_address: "*************",
    user_agent: "Mozilla/5.0...",
    statut: "SUCCESS"
  },
  {
    id: 2,
    timestamp: "2025-05-27T14:25:00Z",
    utilisateur: "<EMAIL>",
    action: "EXPORT_RAPPORT",
    ressource: "Rapport mensuel Mai 2025",
    ip_address: "*************",
    user_agent: "Mozilla/5.0...",
    statut: "SUCCESS"
  },
  {
    id: 3,
    timestamp: "2025-05-27T14:20:00Z",
    utilisateur: "<EMAIL>",
    action: "LOGIN",
    ressource: "Authentification",
    ip_address: "*************",
    user_agent: "Mozilla/5.0...",
    statut: "SUCCESS"
  }
];

// Paramètres IA mockés
export const mockIASettings = {
  seuil_detection: 0.75,
  seuil_alerte_critique: 0.90,
  seuil_alerte_moyenne: 0.70,
  frequence_analyse: "quotidienne",
  types_changements: [
    "deforestation",
    "excavation",
    "activite_miniere",
    "construction_illegale"
  ],
  derniere_mise_a_jour: "2025-05-27T10:00:00Z"
};

// Fonction utilitaire pour simuler les délais d'API
export const simulateApiDelay = (ms = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Simulation d'authentification
export const mockAuth = {
  login: async (email, password) => {
    await simulateApiDelay(1500);
    const user = mockUsers.find(u => u.email === email);
    if (user && password === "password123") {
      return {
        success: true,
        user,
        token: "mock-jwt-token-" + Date.now(),
        expires_in: 1800 // 30 minutes
      };
    }
    return {
      success: false,
      error: "Identifiants invalides"
    };
  },
  
  verify2FA: async (code) => {
    await simulateApiDelay(800);
    return code === "123456" ? { success: true } : { success: false, error: "Code invalide" };
  },
  
  logout: async () => {
    await simulateApiDelay(500);
    return { success: true };
  }
};
