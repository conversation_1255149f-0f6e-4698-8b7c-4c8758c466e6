@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles personnalisés pour l'application gouvernementale */
@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
  }
}

@layer components {
  /* Boutons gouvernementaux */
  .btn-primary {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-300;
  }

  /* Cartes */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 p-6 transition-all duration-300 hover:shadow-xl;
  }

  /* Inputs */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200;
  }

  /* Navigation */
  .nav-link {
    @apply flex items-center px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-green-700 rounded-lg transition-all duration-200;
  }

  .nav-link.active {
    @apply bg-green-100 text-green-800 font-medium;
  }

  /* Alertes */
  .alert-critical {
    @apply bg-red-50 border-l-4 border-red-500 p-4 rounded-r-lg;
  }

  .alert-warning {
    @apply bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-r-lg;
  }

  .alert-info {
    @apply bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg;
  }

  .alert-success {
    @apply bg-green-50 border-l-4 border-green-500 p-4 rounded-r-lg;
  }
}
