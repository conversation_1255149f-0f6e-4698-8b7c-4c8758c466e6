@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS - Thème Côte d'Ivoire */
:root {
  /* Couleurs du drapeau ivoirien modernisées */
  --orange-ci-50: #fff7ed;
  --orange-ci-100: #ffedd5;
  --orange-ci-200: #fed7aa;
  --orange-ci-300: #fdba74;
  --orange-ci-400: #fb923c;
  --orange-ci-500: #FF8C00;
  --orange-ci-600: #ea580c;
  --orange-ci-700: #c2410c;
  --orange-ci-800: #9a3412;
  --orange-ci-900: #7c2d12;

  --vert-ci-50: #f0fdf4;
  --vert-ci-100: #dcfce7;
  --vert-ci-200: #bbf7d0;
  --vert-ci-300: #86efac;
  --vert-ci-400: #4ade80;
  --vert-ci-500: #22C55E;
  --vert-ci-600: #16a34a;
  --vert-ci-700: #15803d;
  --vert-ci-800: #166534;
  --vert-ci-900: #14532d;

  /* Couleurs système */
  --primary-50: var(--vert-ci-50);
  --primary-100: var(--vert-ci-100);
  --primary-200: var(--vert-ci-200);
  --primary-300: var(--vert-ci-300);
  --primary-400: var(--vert-ci-400);
  --primary-500: var(--vert-ci-500);
  --primary-600: var(--vert-ci-600);
  --primary-700: var(--vert-ci-700);
  --primary-800: var(--vert-ci-800);
  --primary-900: var(--vert-ci-900);

  --secondary-50: var(--orange-ci-50);
  --secondary-100: var(--orange-ci-100);
  --secondary-200: var(--orange-ci-200);
  --secondary-300: var(--orange-ci-300);
  --secondary-400: var(--orange-ci-400);
  --secondary-500: var(--orange-ci-500);
  --secondary-600: var(--orange-ci-600);
  --secondary-700: var(--orange-ci-700);
  --secondary-800: var(--orange-ci-800);
  --secondary-900: var(--orange-ci-900);

  --gold-ci: #FFD700;
  --terre-ci: #8B4513;
  --cacao-ci: #D2691E;
}

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2.5 px-5 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-medium py-2.5 px-5 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply border-2 border-primary-300 hover:border-primary-500 text-primary-700 hover:text-white hover:bg-primary-500 font-medium py-2.5 px-5 rounded-xl transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-ci {
    background: linear-gradient(135deg, #ffffff 0%, #fff7ed 50%, #f0fdf4 100%);
    @apply rounded-2xl shadow-xl border-2 p-6 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2;
    border-image: linear-gradient(135deg, var(--orange-ci-200), var(--vert-ci-200)) 1;
  }

  .gradient-ci {
    background: linear-gradient(135deg, var(--orange-ci-500) 0%, var(--vert-ci-500) 100%);
  }

  .text-gradient-ci {
    background: linear-gradient(135deg, var(--orange-ci-600) 0%, var(--vert-ci-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Animations personnalisées avec thème ivoirien */
@keyframes fade-in-ci {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up-ci {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-ci {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 140, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 140, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 140, 0, 0);
  }
}

@keyframes shimmer-ci {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fade-in-ci 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up-ci 0.4s ease-out;
}

.animate-pulse-ci {
  animation: pulse-ci 2s infinite;
}

.animate-shimmer-ci {
  background: linear-gradient(90deg, transparent, rgba(255, 140, 0, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer-ci 1.5s infinite;
}

/* Styles pour Leaflet avec thème ivoirien */
.leaflet-container {
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, var(--orange-ci-200), var(--vert-ci-200)) border-box;
}

.leaflet-popup-content-wrapper {
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--orange-ci-200);
}

.leaflet-popup-content {
  margin: 0.75rem;
  font-family: 'Inter', sans-serif;
}

.leaflet-popup-tip {
  background: white;
  border: 1px solid var(--orange-ci-200);
}

/* Contrôles de zoom personnalisés */
.leaflet-control-zoom a {
  background: linear-gradient(135deg, var(--orange-ci-500), var(--vert-ci-500));
  color: white;
  border-radius: 0.5rem;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.leaflet-control-zoom a:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
}

/* Contrôles de couches personnalisés */
.leaflet-control-layers {
  border-radius: 0.75rem;
  border: 2px solid var(--orange-ci-200);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.leaflet-control-layers-toggle {
  background: linear-gradient(135deg, var(--orange-ci-500), var(--vert-ci-500));
  border-radius: 0.5rem;
}

/* Marqueurs personnalisés Côte d'Ivoire */
.marker-ci-critical {
  background: var(--orange-ci-500);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(255, 140, 0, 0.3);
  animation: pulse-ci 2s infinite;
  width: 20px;
  height: 20px;
}

.marker-ci-warning {
  background: var(--gold-ci);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
  width: 16px;
  height: 16px;
}

.marker-ci-safe {
  background: var(--vert-ci-500);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
  width: 16px;
  height: 16px;
}

/* Scrollbar avec thème ivoirien */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--orange-ci-400), var(--vert-ci-400));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--orange-ci-500), var(--vert-ci-500));
}

/* Badges et indicateurs */
.badge-ci-orange {
  background: linear-gradient(135deg, var(--orange-ci-100), var(--orange-ci-200));
  color: var(--orange-ci-800);
  border: 1px solid var(--orange-ci-300);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-ci-vert {
  background: linear-gradient(135deg, var(--vert-ci-100), var(--vert-ci-200));
  color: var(--vert-ci-800);
  border: 1px solid var(--vert-ci-300);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-ci-gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #8B4513;
  border: 1px solid #DAA520;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
}
