/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9f0',
          100: '#dcf2dc',
          200: '#bce5bc',
          300: '#8dd18d',
          400: '#5bb85b',
          500: '#2E7D32',
          600: '#256d29',
          700: '#1f5a22',
          800: '#1c481e',
          900: '#193c1a',
        },
        secondary: {
          50: '#fffef7',
          100: '#fffce8',
          200: '#fff8c5',
          300: '#fff197',
          400: '#ffe658',
          500: '#FFD700',
          600: '#e6c200',
          700: '#cc9f02',
          800: '#a67c08',
          900: '#87650b',
        },
        accent: {
          50: '#f9f9f9',
          100: '#f3f3f3',
          200: '#e8e8e8',
          300: '#d6d6d6',
          400: '#b4b4b4',
          500: '#E0E0E0',
          600: '#999999',
          700: '#7d7d7d',
          800: '#666666',
          900: '#525252',
        },
        government: {
          blue: '#1E3A8A',
          darkBlue: '#1E40AF',
          lightBlue: '#3B82F6',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
